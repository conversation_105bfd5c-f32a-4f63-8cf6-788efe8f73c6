-- 直接生成三个表的INSERT语句
-- 在SQL Server查询分析器中执行

-- ========================================
-- 生成 bn_Attachment 表的INSERT语句
-- ========================================

PRINT '-- bn_Attachment 表的INSERT语句:'
PRINT ''

-- 生成列名列表
DECLARE @columns_att NVARCHAR(MAX)
SELECT @columns_att = STUFF((
    SELECT ', ' + COLUMN_NAME
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'bn_Attachment'
    ORDER BY ORDINAL_POSITION
    FOR XML PATH('')
), 1, 2, '')

PRINT 'INSERT INTO bn_Attachment (' + @columns_att + ') VALUES'

-- 生成VALUES语句
EXEC('
SELECT 
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = 1 THEN ''''
        ELSE '',''
    END +
    ''('' + ' + 
    STUFF((
        SELECT ' + '', '' + ' + 
            CASE 
                WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') 
                    THEN 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', ''''''''''''), CHAR(13)+CHAR(10), '' '') + '''''''''' END'
                WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') 
                    THEN 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                WHEN DATA_TYPE = 'date' 
                    THEN 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                WHEN DATA_TYPE = 'bit' 
                    THEN 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
                ELSE 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
            END
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'bn_Attachment'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 7, '') + ' +
    '')'' +
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = (SELECT COUNT(*) FROM bn_Attachment) 
        THEN ''''
        ELSE ''''
    END AS InsertStatement
FROM bn_Attachment
WHERE EXISTS (SELECT 1 FROM bn_Attachment)
')

PRINT 'ON DUPLICATE KEY UPDATE Id = Id;'
PRINT ''
PRINT ''

-- ========================================
-- 生成 SysUser 表的INSERT语句
-- ========================================

PRINT '-- SysUser 表的INSERT语句:'
PRINT ''

DECLARE @columns_user NVARCHAR(MAX)
SELECT @columns_user = STUFF((
    SELECT ', ' + COLUMN_NAME
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'SysUser'
    ORDER BY ORDINAL_POSITION
    FOR XML PATH('')
), 1, 2, '')

PRINT 'INSERT INTO SysUser (' + @columns_user + ') VALUES'

EXEC('
SELECT 
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = 1 THEN ''''
        ELSE '',''
    END +
    ''('' + ' + 
    STUFF((
        SELECT ' + '', '' + ' + 
            CASE 
                WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') 
                    THEN 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', ''''''''''''), CHAR(13)+CHAR(10), '' '') + '''''''''' END'
                WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') 
                    THEN 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                WHEN DATA_TYPE = 'date' 
                    THEN 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                WHEN DATA_TYPE = 'bit' 
                    THEN 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
                ELSE 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
            END
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'SysUser'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 7, '') + ' +
    '')'' +
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = (SELECT COUNT(*) FROM SysUser) 
        THEN ''''
        ELSE ''''
    END AS InsertStatement
FROM SysUser
WHERE EXISTS (SELECT 1 FROM SysUser)
')

PRINT 'ON DUPLICATE KEY UPDATE Id = Id;'
PRINT ''
PRINT ''

-- ========================================
-- 生成 SysUserBelong 表的INSERT语句
-- ========================================

PRINT '-- SysUserBelong 表的INSERT语句:'
PRINT ''

DECLARE @columns_belong NVARCHAR(MAX)
SELECT @columns_belong = STUFF((
    SELECT ', ' + COLUMN_NAME
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'SysUserBelong'
    ORDER BY ORDINAL_POSITION
    FOR XML PATH('')
), 1, 2, '')

PRINT 'INSERT INTO SysUserBelong (' + @columns_belong + ') VALUES'

EXEC('
SELECT 
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = 1 THEN ''''
        ELSE '',''
    END +
    ''('' + ' + 
    STUFF((
        SELECT ' + '', '' + ' + 
            CASE 
                WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') 
                    THEN 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', ''''''''''''), CHAR(13)+CHAR(10), '' '') + '''''''''' END'
                WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') 
                    THEN 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                WHEN DATA_TYPE = 'date' 
                    THEN 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                WHEN DATA_TYPE = 'bit' 
                    THEN 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
                ELSE 'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
            END
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'SysUserBelong'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 7, '') + ' +
    '')'' +
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = (SELECT COUNT(*) FROM SysUserBelong) 
        THEN ''''
        ELSE ''''
    END AS InsertStatement
FROM SysUserBelong
WHERE EXISTS (SELECT 1 FROM SysUserBelong)
')

PRINT 'ON DUPLICATE KEY UPDATE Id = Id;'
PRINT ''

PRINT '-- 脚本执行完成'
