-- 分析源表和目标表的字段差异
-- 帮助了解哪些字段需要使用默认值

USE HyunCoreBase;
GO

DECLARE @TableName NVARCHAR(128);

-- 要分析的表列表
DECLARE table_cursor CURSOR FOR
SELECT 'dc_PurchaseList' AS TableName
UNION ALL
SELECT 'dc_PurchaseOrder'
UNION ALL
SELECT 'dc_PurchaseApproval';

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '========================================';
    PRINT '分析表: ' + @TableName;
    PRINT '========================================';
    
    -- 检查表是否存在
    IF EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName)
       AND EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName)
    BEGIN
        PRINT '';
        PRINT '源表 (Hyun) 字段列表:';
        PRINT '----------------------------------------';
        
        -- 显示源表字段
        SELECT 
            ROW_NUMBER() OVER (ORDER BY ORDINAL_POSITION) AS 序号,
            COLUMN_NAME AS 字段名,
            DATA_TYPE AS 数据类型,
            CASE 
                WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL 
                THEN DATA_TYPE + '(' + CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10)) + ')'
                WHEN NUMERIC_PRECISION IS NOT NULL 
                THEN DATA_TYPE + '(' + CAST(NUMERIC_PRECISION AS VARCHAR(10)) + 
                     CASE WHEN NUMERIC_SCALE > 0 THEN ',' + CAST(NUMERIC_SCALE AS VARCHAR(10)) ELSE '' END + ')'
                ELSE DATA_TYPE
            END AS 完整类型,
            CASE WHEN IS_NULLABLE = 'YES' THEN '是' ELSE '否' END AS 允许空值
        FROM Hyun.INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @TableName
          AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;
        
        PRINT '';
        PRINT '目标表 (HyunCoreBase) 字段列表:';
        PRINT '----------------------------------------';
        
        -- 显示目标表字段
        SELECT 
            ROW_NUMBER() OVER (ORDER BY ORDINAL_POSITION) AS 序号,
            COLUMN_NAME AS 字段名,
            DATA_TYPE AS 数据类型,
            CASE 
                WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL 
                THEN DATA_TYPE + '(' + CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10)) + ')'
                WHEN NUMERIC_PRECISION IS NOT NULL 
                THEN DATA_TYPE + '(' + CAST(NUMERIC_PRECISION AS VARCHAR(10)) + 
                     CASE WHEN NUMERIC_SCALE > 0 THEN ',' + CAST(NUMERIC_SCALE AS VARCHAR(10)) ELSE '' END + ')'
                ELSE DATA_TYPE
            END AS 完整类型,
            CASE WHEN IS_NULLABLE = 'YES' THEN '是' ELSE '否' END AS 允许空值,
            CASE 
                WHEN COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 1 
                THEN '是' ELSE '否' 
            END AS 自增列
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @TableName
          AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;
        
        PRINT '';
        PRINT '字段差异分析:';
        PRINT '----------------------------------------';
        
        -- 分析字段差异
        WITH SourceCols AS (
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM Hyun.INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo'
        ),
        TargetCols AS (
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo'
              AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
        )
        SELECT 
            '目标表独有字段 (需要默认值)' AS 分析类型,
            tc.COLUMN_NAME AS 字段名,
            tc.DATA_TYPE AS 数据类型,
            CASE 
                -- 用户ID字段
                WHEN tc.COLUMN_NAME LIKE '%UserId' OR tc.COLUMN_NAME IN ('CreateUserId', 'UpdateUserId', 'CreatedBy', 'UpdatedBy') 
                THEN '固定用户ID: 592123693924485'
                
                -- 单位ID字段
                WHEN tc.COLUMN_NAME LIKE '%UnitId' OR tc.COLUMN_NAME IN ('OrganizationId', 'DepartmentId')
                THEN '固定单位ID: 592123124052101'
                
                -- 其他字段根据类型提供默认值
                ELSE 
                    CASE 
                        WHEN tc.DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint', 'bit') THEN '默认值: 0'
                        WHEN tc.DATA_TYPE IN ('decimal', 'numeric', 'float', 'real', 'money', 'smallmoney') THEN '默认值: 0.0'
                        WHEN tc.DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN '默认值: '''' (空字符串)'
                        WHEN tc.DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime', 'date', 'time') THEN '默认值: GETDATE()'
                        WHEN tc.DATA_TYPE = 'uniqueidentifier' THEN '默认值: NEWID()'
                        WHEN tc.IS_NULLABLE = 'YES' THEN '默认值: NULL'
                        ELSE '默认值: '''' (空字符串)'
                    END
            END AS 建议默认值
        FROM TargetCols tc
        LEFT JOIN SourceCols sc ON tc.COLUMN_NAME = sc.COLUMN_NAME
        WHERE sc.COLUMN_NAME IS NULL
        
        UNION ALL
        
        SELECT 
            '源表独有字段 (将被忽略)' AS 分析类型,
            sc.COLUMN_NAME AS 字段名,
            sc.DATA_TYPE AS 数据类型,
            '此字段在目标表中不存在，迁移时将被忽略' AS 建议默认值
        FROM SourceCols sc
        LEFT JOIN TargetCols tc ON sc.COLUMN_NAME = tc.COLUMN_NAME
        WHERE tc.COLUMN_NAME IS NULL
        
        ORDER BY 分析类型, 字段名;
        
        PRINT '';
        PRINT '共同字段统计:';
        PRINT '----------------------------------------';
        
        -- 统计共同字段
        WITH SourceCols AS (
            SELECT COLUMN_NAME FROM Hyun.INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo'
        ),
        TargetCols AS (
            SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo'
              AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
        )
        SELECT 
            (SELECT COUNT(*) FROM SourceCols) AS 源表字段总数,
            (SELECT COUNT(*) FROM TargetCols) AS 目标表字段总数,
            (SELECT COUNT(*) FROM SourceCols sc INNER JOIN TargetCols tc ON sc.COLUMN_NAME = tc.COLUMN_NAME) AS 共同字段数,
            (SELECT COUNT(*) FROM TargetCols tc LEFT JOIN SourceCols sc ON tc.COLUMN_NAME = sc.COLUMN_NAME WHERE sc.COLUMN_NAME IS NULL) AS 需要默认值的字段数,
            (SELECT COUNT(*) FROM SourceCols sc LEFT JOIN TargetCols tc ON sc.COLUMN_NAME = tc.COLUMN_NAME WHERE tc.COLUMN_NAME IS NULL) AS 将被忽略的字段数;
        
    END
    ELSE
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName)
            PRINT '源表 Hyun.dbo.' + @TableName + ' 不存在';
        
        IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName)
            PRINT '目标表 HyunCoreBase.dbo.' + @TableName + ' 不存在';
    END
    
    PRINT '';
    PRINT '';
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '========================================';
PRINT '分析完成！';
PRINT '========================================';
GO
