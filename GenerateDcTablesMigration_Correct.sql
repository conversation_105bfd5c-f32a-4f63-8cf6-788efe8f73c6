-- 正确版本：生成所有dc开头表的迁移脚本
-- 逻辑：源表存在的字段直接同步，源表不存在的字段用默认值

USE HyunCoreBase;
GO

DECLARE @userid BIGINT = 592123693924485;
DECLARE @unitId BIGINT = 592123124052101;

PRINT '========================================';
PRINT '生成dc开头表的迁移脚本 (正确版本)';
PRINT '========================================';
PRINT '用户ID: ' + CAST(@userid AS NVARCHAR(20));
PRINT '单位ID: ' + CAST(@unitId AS NVARCHAR(20));
PRINT '';
PRINT '逻辑：';
PRINT '- 源表存在的字段：直接同步源表的值';
PRINT '- 源表不存在的字段：使用默认值';
PRINT '- 特殊字段SchoolId/UnitId：强制使用固定单位ID';
PRINT '- 特殊字段UserId相关：强制使用固定用户ID';
PRINT '';

-- 获取所有以dc开头的表
DECLARE @TableName NVARCHAR(128);

DECLARE table_cursor CURSOR FOR
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME LIKE 'dc%' 
  AND TABLE_TYPE = 'BASE TABLE'
  AND TABLE_SCHEMA = 'dbo'
ORDER BY TABLE_NAME;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '========================================';
    PRINT '-- ' + @TableName + ' 表迁移脚本';
    PRINT '========================================';
    
    -- 检查源表是否存在
    IF EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo')
    BEGIN
        PRINT 'PRINT ''正在迁移 ' + @TableName + ' 表...'';';
        PRINT '';
        PRINT 'BEGIN TRY';
        PRINT '    INSERT INTO dbo.' + @TableName;
        PRINT '    (';
        
        -- 生成INSERT字段列表
        DECLARE @IsFirstColumn BIT = 1;
        DECLARE @ColumnName NVARCHAR(128);
        
        DECLARE column_cursor CURSOR FOR
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = @TableName 
          AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
          AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;
        
        OPEN column_cursor;
        FETCH NEXT FROM column_cursor INTO @ColumnName;
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            IF @IsFirstColumn = 1
            BEGIN
                PRINT '        ' + @ColumnName;
                SET @IsFirstColumn = 0;
            END
            ELSE
            BEGIN
                PRINT '        ,' + @ColumnName;
            END
            
            FETCH NEXT FROM column_cursor INTO @ColumnName;
        END
        
        CLOSE column_cursor;
        DEALLOCATE column_cursor;
        
        PRINT '    )';
        PRINT '    SELECT';
        
        -- 生成SELECT字段列表
        SET @IsFirstColumn = 1;
        
        DECLARE column_cursor2 CURSOR FOR
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = @TableName 
          AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
          AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;
        
        DECLARE @DataType NVARCHAR(128);
        OPEN column_cursor2;
        FETCH NEXT FROM column_cursor2 INTO @ColumnName, @DataType;
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            DECLARE @SelectValue NVARCHAR(500);
            DECLARE @SourceHasColumn BIT = 0;
            
            -- 检查源表是否有此字段
            IF EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.COLUMNS 
                      WHERE TABLE_NAME = @TableName AND COLUMN_NAME = @ColumnName AND TABLE_SCHEMA = 'dbo')
                SET @SourceHasColumn = 1;
            
            -- 根据是否存在于源表来决定SELECT值
            IF @SourceHasColumn = 1
            BEGIN
                -- 源表有此字段，检查是否需要特殊处理
                IF @ColumnName IN ('SchoolId', 'UnitId', 'OrganizationId', 'DepartmentId')
                    SET @SelectValue = CAST(@unitId AS NVARCHAR(20)) + ' AS ' + @ColumnName + ' -- 固定单位ID';
                ELSE IF @ColumnName LIKE '%UserId' OR @ColumnName IN ('CreateUserId', 'UpdateUserId', 'CreatedBy', 'UpdatedBy', 'CreateId', 'ModifyId', 'CreateBy', 'ModifyBy')
                    SET @SelectValue = CAST(@userid AS NVARCHAR(20)) + ' AS ' + @ColumnName + ' -- 固定用户ID';
                ELSE
                    SET @SelectValue = 'src.' + @ColumnName + ' -- 源表字段';
            END
            ELSE
            BEGIN
                -- 源表没有此字段，使用默认值
                IF @ColumnName IN ('SchoolId', 'UnitId', 'OrganizationId', 'DepartmentId')
                    SET @SelectValue = CAST(@unitId AS NVARCHAR(20)) + ' AS ' + @ColumnName + ' -- 默认单位ID';
                ELSE IF @ColumnName LIKE '%UserId' OR @ColumnName IN ('CreateUserId', 'UpdateUserId', 'CreatedBy', 'UpdatedBy', 'CreateId', 'ModifyId', 'CreateBy', 'ModifyBy')
                    SET @SelectValue = CAST(@userid AS NVARCHAR(20)) + ' AS ' + @ColumnName + ' -- 默认用户ID';
                ELSE IF @ColumnName IN ('CreateTime', 'ModifyTime', 'UpdateTime', 'RegDate')
                    SET @SelectValue = 'GETDATE() AS ' + @ColumnName + ' -- 默认时间';
                ELSE IF @ColumnName IN ('IsDeleted', 'Deleted', 'IsDelete')
                    SET @SelectValue = '0 AS ' + @ColumnName + ' -- 默认未删除';
                ELSE IF @ColumnName IN ('Version', 'VersionNo')
                    SET @SelectValue = '0 AS ' + @ColumnName + ' -- 默认版本';
                ELSE IF @ColumnName IN ('Status', 'Statuz', 'State')
                    SET @SelectValue = '1 AS ' + @ColumnName + ' -- 默认状态';
                ELSE IF @DataType IN ('int', 'bigint', 'smallint', 'tinyint', 'bit')
                    SET @SelectValue = '0 AS ' + @ColumnName + ' -- 默认数值';
                ELSE IF @DataType IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext')
                    SET @SelectValue = ''''' AS ' + @ColumnName + ' -- 默认字符串';
                ELSE IF @DataType IN ('datetime', 'datetime2', 'smalldatetime', 'date', 'time')
                    SET @SelectValue = 'GETDATE() AS ' + @ColumnName + ' -- 默认日期';
                ELSE IF @DataType = 'uniqueidentifier'
                    SET @SelectValue = 'NEWID() AS ' + @ColumnName + ' -- 默认GUID';
                ELSE
                    SET @SelectValue = 'NULL AS ' + @ColumnName + ' -- 默认NULL';
            END
            
            IF @IsFirstColumn = 1
            BEGIN
                PRINT '        ' + @SelectValue;
                SET @IsFirstColumn = 0;
            END
            ELSE
            BEGIN
                PRINT '        ,' + @SelectValue;
            END
            
            FETCH NEXT FROM column_cursor2 INTO @ColumnName, @DataType;
        END
        
        CLOSE column_cursor2;
        DEALLOCATE column_cursor2;
        
        PRINT '    FROM Hyun.dbo.' + @TableName + ' src';
        PRINT '    WHERE NOT EXISTS (';
        PRINT '        SELECT 1 FROM ' + @TableName + ' target';
        PRINT '        WHERE target.Id = src.Id';
        PRINT '    );';
        PRINT '';
        PRINT '    PRINT ''' + @TableName + ' 表迁移完成，新增行数: '' + CAST(@@ROWCOUNT AS NVARCHAR(10));';
        PRINT 'END TRY';
        PRINT 'BEGIN CATCH';
        PRINT '    PRINT ''' + @TableName + ' 表迁移失败: '' + ERROR_MESSAGE();';
        PRINT '    PRINT ''请检查字段映射是否正确'';';
        PRINT 'END CATCH';
        PRINT '';
        PRINT 'PRINT '''';';
        PRINT '';
    END
    ELSE
    BEGIN
        PRINT '-- 源表 Hyun.dbo.' + @TableName + ' 不存在，跳过';
        PRINT '';
    END
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '========================================';
PRINT '-- 脚本生成完成！';
PRINT '========================================';
PRINT '';
PRINT '/*';
PRINT '使用说明：';
PRINT '1. 复制上面生成的INSERT语句到新查询窗口执行';
PRINT '2. 如果某个字段报错，说明源表和目标表的字段类型不匹配';
PRINT '3. 手动调整报错的字段，使用合适的默认值或类型转换';
PRINT '';
PRINT '生成逻辑：';
PRINT '- 源表存在的字段：直接使用 src.字段名';
PRINT '- 源表不存在的字段：使用对应数据类型的默认值';
PRINT '- SchoolId/UnitId：强制使用 ' + CAST(@unitId AS NVARCHAR(20));
PRINT '- UserId相关字段：强制使用 ' + CAST(@userid AS NVARCHAR(20));
PRINT '*/';

GO
