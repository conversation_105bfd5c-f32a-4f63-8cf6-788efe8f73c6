-- 自动生成INSERT INTO SELECT脚本的工具
-- 根据目标表结构自动生成迁移脚本，处理缺失字段

USE HyunCoreBase;
GO

DECLARE @FixedUserId NVARCHAR(50) = '592123693924485';
DECLARE @FixedUnitId NVARCHAR(50) = '592123124052101';
DECLARE @TableName NVARCHAR(128);
DECLARE @GeneratedSQL NVARCHAR(MAX) = '';

-- 要处理的表列表
DECLARE table_cursor CURSOR FOR
SELECT 'dc_PurchaseList' AS TableName
UNION ALL
SELECT 'dc_PurchaseOrder'
UNION ALL
SELECT 'dc_PurchaseApproval';

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    DECLARE @ColumnList NVARCHAR(MAX) = '';
    DECLARE @SelectList NVARCHAR(MAX) = '';
    
    PRINT '-- ========================================';
    PRINT '-- 生成 ' + @TableName + ' 表的INSERT脚本';
    PRINT '-- ========================================';
    
    -- 检查表是否存在
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName)
    BEGIN
        -- 生成列列表和SELECT列表
        SELECT 
            @ColumnList = @ColumnList + 
                CASE WHEN @ColumnList = '' THEN '' ELSE ',' + CHAR(13) + '        ' END + 
                COLUMN_NAME,
            @SelectList = @SelectList + 
                CASE WHEN @SelectList = '' THEN '' ELSE ',' + CHAR(13) + '        ' END + 
                CASE 
                    -- 处理用户ID字段
                    WHEN COLUMN_NAME LIKE '%UserId' OR COLUMN_NAME IN ('CreateUserId', 'UpdateUserId', 'CreatedBy', 'UpdatedBy') 
                    THEN '''' + @FixedUserId + ''' AS ' + COLUMN_NAME
                    
                    -- 处理单位ID字段
                    WHEN COLUMN_NAME LIKE '%UnitId' OR COLUMN_NAME IN ('OrganizationId', 'DepartmentId')
                    THEN '''' + @FixedUnitId + ''' AS ' + COLUMN_NAME
                    
                    -- 其他字段使用ISNULL处理，根据数据类型提供默认值
                    ELSE 
                        'ISNULL(src.' + COLUMN_NAME + ', ' +
                        CASE 
                            -- 数值类型默认值
                            WHEN DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint', 'bit') THEN '0'
                            WHEN DATA_TYPE IN ('decimal', 'numeric', 'float', 'real', 'money', 'smallmoney') THEN '0.0'
                            
                            -- 字符串类型默认值
                            WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN ''''''
                            
                            -- 日期时间类型默认值
                            WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime', 'date', 'time') THEN 'GETDATE()'
                            
                            -- GUID类型默认值
                            WHEN DATA_TYPE = 'uniqueidentifier' THEN 'NEWID()'
                            
                            -- 其他类型默认为空字符串
                            ELSE ''''''
                        END + ') AS ' + COLUMN_NAME
                END
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = @TableName 
          AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
          AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;

        -- 生成完整的INSERT语句
        SET @GeneratedSQL = 
            'PRINT ''正在迁移 ' + @TableName + ' 表...'';' + CHAR(13) + CHAR(13) +
            'BEGIN TRY' + CHAR(13) +
            '    INSERT INTO ' + @TableName + ' (' + CHAR(13) +
            '        ' + @ColumnList + CHAR(13) +
            '    )' + CHAR(13) +
            '    SELECT ' + CHAR(13) +
            '        ' + @SelectList + CHAR(13) +
            '    FROM Hyun.dbo.' + @TableName + ' src' + CHAR(13) +
            '    WHERE NOT EXISTS (' + CHAR(13) +
            '        SELECT 1 FROM ' + @TableName + ' target ' + CHAR(13) +
            '        WHERE target.Id = src.Id' + CHAR(13) +
            '    );' + CHAR(13) + CHAR(13) +
            '    PRINT ''' + @TableName + ' 表迁移完成，新增行数: '' + CAST(@@ROWCOUNT AS NVARCHAR(10));' + CHAR(13) +
            'END TRY' + CHAR(13) +
            'BEGIN CATCH' + CHAR(13) +
            '    PRINT ''' + @TableName + ' 表迁移失败: '' + ERROR_MESSAGE();' + CHAR(13) +
            'END CATCH' + CHAR(13) + CHAR(13) +
            'PRINT '''';' + CHAR(13) + CHAR(13);

        -- 输出生成的SQL
        PRINT @GeneratedSQL;
    END
    ELSE
    BEGIN
        PRINT '-- 表 ' + @TableName + ' 不存在';
        PRINT '';
    END
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '-- ========================================';
PRINT '-- 脚本生成完成';
PRINT '-- ========================================';
GO

-- 使用说明
PRINT '/*';
PRINT '使用说明：';
PRINT '1. 运行此脚本会生成针对每个表的INSERT INTO SELECT语句';
PRINT '2. 生成的脚本会自动处理：';
PRINT '   - 用户ID字段替换为固定值: 592123693924485';
PRINT '   - 单位ID字段替换为固定值: 592123124052101';
PRINT '   - 缺失字段使用相应数据类型的默认值';
PRINT '   - 避免重复插入（通过Id字段检查）';
PRINT '3. 复制生成的SQL语句到新查询窗口执行即可';
PRINT '*/';
GO
