-- 快速MySQL导出脚本
-- 直接生成可在MySQL中执行的INSERT语句
-- 使用INSERT IGNORE来避免重复插入

USE syjx;
GO

DECLARE @TableName NVARCHAR(128);
DECLARE @SQL NVARCHAR(MAX);

PRINT '-- MySQL数据导入脚本';
PRINT '-- 生成时间: ' + CONVERT(VARCHAR(19), GETDATE(), 120);
PRINT '-- 使用INSERT IGNORE避免重复插入';
PRINT '';

-- 处理所有有id字段的表
DECLARE table_cursor CURSOR FOR
SELECT TABLE_NAME
FROM INFORMATION_SCHEMA.TABLES t
WHERE TABLE_TYPE = 'BASE TABLE'
AND EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS c
    WHERE c.TABLE_NAME = t.TABLE_NAME 
    AND c.COLUMN_NAME = 'id'
    AND c.DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint')
)
ORDER BY TABLE_NAME;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '-- ======================================================';
    PRINT '-- 表: ' + @TableName;
    PRINT '-- ======================================================';
    
    -- 生成表的行数统计
    SET @SQL = 'SELECT COUNT(*) as RowCount FROM ' + @TableName;
    PRINT '-- 记录数统计:';
    PRINT @SQL + ';';
    PRINT '';
    
    -- 生成INSERT IGNORE语句
    PRINT '-- MySQL INSERT语句:';
    
    -- 获取列名
    DECLARE @Columns NVARCHAR(MAX);
    SELECT @Columns = STRING_AGG('`' + COLUMN_NAME + '`', ', ')
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = @TableName
    ORDER BY ORDINAL_POSITION;
    
    PRINT 'INSERT IGNORE INTO `' + @TableName + '` (' + @Columns + ')';
    
    -- 生成SELECT语句用于导出数据
    PRINT 'SELECT ';
    
    DECLARE @SelectList NVARCHAR(MAX) = '';
    SELECT @SelectList = @SelectList + 
        CASE 
            WHEN @SelectList != '' THEN ',' + CHAR(13) + '    '
            ELSE '    '
        END +
        CASE 
            WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                'CASE WHEN ' + COLUMN_NAME + ' IS NULL THEN ''NULL'' ELSE '''''''' + REPLACE(REPLACE(REPLACE(' + COLUMN_NAME + ', '''''''', ''''''''''''), CHAR(13), ''''), CHAR(10), '''') + '''''''' END'
            WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
                'CASE WHEN ' + COLUMN_NAME + ' IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(19), ' + COLUMN_NAME + ', 120) + '''''''' END'
            WHEN DATA_TYPE IN ('date') THEN
                'CASE WHEN ' + COLUMN_NAME + ' IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(10), ' + COLUMN_NAME + ', 120) + '''''''' END'
            WHEN DATA_TYPE IN ('time') THEN
                'CASE WHEN ' + COLUMN_NAME + ' IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(8), ' + COLUMN_NAME + ', 108) + '''''''' END'
            WHEN DATA_TYPE IN ('bit') THEN
                'CAST(ISNULL(' + COLUMN_NAME + ', 0) AS VARCHAR(1))'
            WHEN DATA_TYPE IN ('decimal', 'numeric', 'money', 'smallmoney', 'float', 'real') THEN
                'CASE WHEN ' + COLUMN_NAME + ' IS NULL THEN ''NULL'' ELSE CAST(' + COLUMN_NAME + ' AS VARCHAR(50)) END'
            ELSE
                'CASE WHEN ' + COLUMN_NAME + ' IS NULL THEN ''NULL'' ELSE CAST(' + COLUMN_NAME + ' AS VARCHAR(MAX)) END'
        END
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = @TableName
    ORDER BY ORDINAL_POSITION;
    
    PRINT @SelectList;
    PRINT 'FROM ' + @TableName;
    PRINT 'ORDER BY id;';
    PRINT '';
    
    -- 生成使用说明
    PRINT '-- 使用方法:';
    PRINT '-- 1. 在SQL Server中执行上面的SELECT语句';
    PRINT '-- 2. 将结果复制，每行格式化为: (value1, value2, ...)';
    PRINT '-- 3. 在MySQL中执行完整的INSERT IGNORE语句';
    PRINT '';
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '-- ======================================================';
PRINT '-- 完成！';
PRINT '-- 注意：INSERT IGNORE会自动跳过重复的id，确保数据不重复';
PRINT '-- ======================================================';
GO
