# Simple PowerShell Script for SQL Server to MySQL Export
# Fixed version without syntax errors

param(
    [string]$Server = "***********",
    [string]$Database = "syjx", 
    [string]$User = "hyun",
    [string]$Pass = "hyun",
    [string]$OutputDir = "mysql_scripts"
)

Write-Host "SQL Server to MySQL Exporter" -ForegroundColor Green
Write-Host "Server: $Server" -ForegroundColor Yellow
Write-Host "Database: $Database" -ForegroundColor Yellow
Write-Host ""

# Create output directory
if (!(Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    Write-Host "Created directory: $OutputDir" -ForegroundColor Green
}

try {
    # Connection string
    $connStr = "Server=$Server;Database=$Database;User Id=$User;Password=$Pass;TrustServerCertificate=true;"
    
    # Create connection
    $conn = New-Object System.Data.SqlClient.SqlConnection($connStr)
    $conn.Open()
    
    Write-Host "Connected to SQL Server" -ForegroundColor Green
    
    # Get tables with id field
    $tablesQuery = @"
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES t
WHERE TABLE_TYPE = 'BASE TABLE'
AND EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS c
    WHERE c.TABLE_NAME = t.TABLE_NAME 
    AND c.COLUMN_NAME = 'id'
    AND c.DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint')
)
ORDER BY TABLE_NAME
"@
    
    $cmd = New-Object System.Data.SqlClient.SqlCommand($tablesQuery, $conn)
    $reader = $cmd.ExecuteReader()
    
    $tables = @()
    while ($reader.Read()) {
        $tables += $reader.GetString(0)
    }
    $reader.Close()
    
    Write-Host "Found $($tables.Count) tables with id field" -ForegroundColor Cyan
    Write-Host ""
    
    foreach ($table in $tables) {
        Write-Host "Processing: $table" -ForegroundColor Yellow
        
        # Check row count
        $countCmd = New-Object System.Data.SqlClient.SqlCommand("SELECT COUNT(*) FROM [$table]", $conn)
        $rowCount = $countCmd.ExecuteScalar()
        
        if ($rowCount -gt 0) {
            Write-Host "  Rows: $rowCount" -ForegroundColor Cyan
            
            # Get columns
            $colQuery = @"
SELECT COLUMN_NAME, DATA_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = '$table' 
ORDER BY ORDINAL_POSITION
"@
            
            $colCmd = New-Object System.Data.SqlClient.SqlCommand($colQuery, $conn)
            $colReader = $colCmd.ExecuteReader()
            
            $columns = @()
            while ($colReader.Read()) {
                $columns += @{
                    Name = $colReader.GetString(0)
                    Type = $colReader.GetString(1)
                }
            }
            $colReader.Close()
            
            # Generate script content
            $script = "-- MySQL Insert Script - Table: $table`n"
            $script += "-- Generated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')`n"
            $script += "-- Row Count: $rowCount`n"
            $script += "-- Note: Using INSERT IGNORE to skip duplicate ids`n`n"
            
            # Column names
            $colNames = ($columns | ForEach-Object { "``$($_.Name)``" }) -join ", "
            $script += "INSERT IGNORE INTO ``$table`` ($colNames) VALUES`n"
            
            # Get data
            $dataCmd = New-Object System.Data.SqlClient.SqlCommand("SELECT * FROM [$table] ORDER BY id", $conn)
            $dataReader = $dataCmd.ExecuteReader()
            
            $values = @()
            while ($dataReader.Read()) {
                $rowVals = @()
                for ($i = 0; $i -lt $columns.Count; $i++) {
                    if ($dataReader.IsDBNull($i)) {
                        $rowVals += "NULL"
                    } else {
                        $val = $dataReader.GetValue($i)
                        $colType = $columns[$i].Type
                        
                        switch ($colType) {
                            { $_ -in @('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') } {
                                $escaped = $val.ToString().Replace("'", "''").Replace("`r", "").Replace("`n", " ")
                                $rowVals += "'$escaped'"
                            }
                            { $_ -in @('datetime', 'datetime2', 'smalldatetime') } {
                                $date = [DateTime]$val
                                $rowVals += "'$($date.ToString('yyyy-MM-dd HH:mm:ss'))'"
                            }
                            { $_ -eq 'date' } {
                                $date = [DateTime]$val
                                $rowVals += "'$($date.ToString('yyyy-MM-dd'))'"
                            }
                            { $_ -eq 'bit' } {
                                $rowVals += if ($val) { "1" } else { "0" }
                            }
                            default {
                                $rowVals += $val.ToString()
                            }
                        }
                    }
                }
                $values += "(" + ($rowVals -join ", ") + ")"
            }
            $dataReader.Close()
            
            # Complete script
            $script += ($values -join ",`n") + ";"
            
            # Save file
            $fileName = "$table" + "_mysql_insert.sql"
            $filePath = Join-Path $OutputDir $fileName
            $script | Out-File -FilePath $filePath -Encoding UTF8
            
            Write-Host "  Generated: $fileName" -ForegroundColor Green
        } else {
            Write-Host "  Skipping empty table" -ForegroundColor Gray
        }
    }
    
    Write-Host ""
    Write-Host "Export completed!" -ForegroundColor Green
    Write-Host "Files saved to: $OutputDir" -ForegroundColor Green
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($conn -and $conn.State -eq 'Open') {
        $conn.Close()
        Write-Host "Connection closed" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
