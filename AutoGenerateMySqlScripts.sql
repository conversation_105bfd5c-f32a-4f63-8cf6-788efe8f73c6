-- 自动生成MySQL脚本文件
-- 为每个表生成独立的MySQL INSERT脚本文件
-- 自动判断id是否存在，不存在则插入

USE syjx;
GO

-- 声明变量
DECLARE @TableName NVARCHAR(128);
DECLARE @SQL NVARCHAR(MAX);
DECLARE @MySqlScript NVARCHAR(MAX);
DECLARE @ColumnList NVARCHAR(MAX);
DECLARE @ValuesList NVARCHAR(MAX);
DECLARE @RowCount INT;
DECLARE @FileName NVARCHAR(255);

PRINT '开始生成MySQL脚本文件...';
PRINT '======================================================';

-- 获取所有有id字段且有数据的表
DECLARE table_cursor CURSOR FOR
SELECT t.TABLE_NAME
FROM INFORMATION_SCHEMA.TABLES t
WHERE t.TABLE_TYPE = 'BASE TABLE'
AND EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS c
    WHERE c.TABLE_NAME = t.TABLE_NAME 
    AND c.COLUMN_NAME = 'id'
    AND c.DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint')
)
ORDER BY t.TABLE_NAME;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    -- 检查表是否有数据
    SET @SQL = 'SELECT @count = COUNT(*) FROM ' + @TableName;
    EXEC sp_executesql @SQL, N'@count INT OUTPUT', @count = @RowCount OUTPUT;
    
    IF @RowCount > 0
    BEGIN
        PRINT '正在处理表: ' + @TableName + ' (记录数: ' + CAST(@RowCount AS VARCHAR(10)) + ')';
        
        -- 生成文件名
        SET @FileName = @TableName + '_mysql_insert.sql';
        
        -- 开始生成MySQL脚本内容
        SET @MySqlScript = '-- MySQL插入脚本 - 表: ' + @TableName + CHAR(13) + CHAR(10);
        SET @MySqlScript = @MySqlScript + '-- 生成时间: ' + CONVERT(VARCHAR(19), GETDATE(), 120) + CHAR(13) + CHAR(10);
        SET @MySqlScript = @MySqlScript + '-- 记录数: ' + CAST(@RowCount AS VARCHAR(10)) + CHAR(13) + CHAR(10);
        SET @MySqlScript = @MySqlScript + '-- 说明: 使用INSERT IGNORE自动跳过重复的id' + CHAR(13) + CHAR(10);
        SET @MySqlScript = @MySqlScript + CHAR(13) + CHAR(10);
        
        -- 获取列名列表
        SET @ColumnList = '';
        DECLARE @ColumnName NVARCHAR(128);
        
        DECLARE column_cursor CURSOR FOR
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @TableName
        ORDER BY ORDINAL_POSITION;
        
        OPEN column_cursor;
        FETCH NEXT FROM column_cursor INTO @ColumnName;
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            IF @ColumnList != ''
                SET @ColumnList = @ColumnList + ', ';
            SET @ColumnList = @ColumnList + '`' + @ColumnName + '`';
            
            FETCH NEXT FROM column_cursor INTO @ColumnName;
        END
        
        CLOSE column_cursor;
        DEALLOCATE column_cursor;
        
        -- 添加INSERT语句头部
        SET @MySqlScript = @MySqlScript + 'INSERT IGNORE INTO `' + @TableName + '` (' + @ColumnList + ') VALUES' + CHAR(13) + CHAR(10);
        
        -- 生成VALUES部分
        DECLARE @IsFirstRow BIT = 1;
        DECLARE @ValueRow NVARCHAR(MAX);
        
        -- 动态构建数据查询
        SET @SQL = '
        DECLARE @Values NVARCHAR(MAX);
        DECLARE @IsFirst BIT = 1;
        DECLARE @Result NVARCHAR(MAX) = '''';
        
        DECLARE data_cursor CURSOR FOR
        SELECT ';
        
        -- 构建SELECT列表
        DECLARE @SelectColumns NVARCHAR(MAX) = '';
        DECLARE @DataType NVARCHAR(128);
        
        DECLARE select_cursor CURSOR FOR
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = ''' + @TableName + '''
        ORDER BY ORDINAL_POSITION;
        
        OPEN select_cursor;
        FETCH NEXT FROM select_cursor INTO @ColumnName, @DataType;
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            IF @SelectColumns != ''
                SET @SelectColumns = @SelectColumns + ' + '', '' + ';
                
            SET @SelectColumns = @SelectColumns + 
                CASE 
                    WHEN @DataType IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                        'CASE WHEN ' + @ColumnName + ' IS NULL THEN ''NULL'' ELSE '''''''' + REPLACE(REPLACE(REPLACE(' + @ColumnName + ', '''''''', ''''''''''''), CHAR(13), '' ''), CHAR(10), '' '') + '''''''' END'
                    WHEN @DataType IN ('datetime', 'datetime2', 'smalldatetime') THEN
                        'CASE WHEN ' + @ColumnName + ' IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(19), ' + @ColumnName + ', 120) + '''''''' END'
                    WHEN @DataType IN ('date') THEN
                        'CASE WHEN ' + @ColumnName + ' IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(10), ' + @ColumnName + ', 120) + '''''''' END'
                    WHEN @DataType IN ('time') THEN
                        'CASE WHEN ' + @ColumnName + ' IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(8), ' + @ColumnName + ', 108) + '''''''' END'
                    WHEN @DataType IN ('bit') THEN
                        'CAST(ISNULL(' + @ColumnName + ', 0) AS VARCHAR(1))'
                    WHEN @DataType IN ('decimal', 'numeric', 'money', 'smallmoney', 'float', 'real') THEN
                        'CASE WHEN ' + @ColumnName + ' IS NULL THEN ''NULL'' ELSE CAST(' + @ColumnName + ' AS VARCHAR(50)) END'
                    ELSE
                        'CASE WHEN ' + @ColumnName + ' IS NULL THEN ''NULL'' ELSE CAST(' + @ColumnName + ' AS VARCHAR(MAX)) END'
                END;
                
            FETCH NEXT FROM select_cursor INTO @ColumnName, @DataType;
        END
        
        CLOSE select_cursor;
        DEALLOCATE select_cursor;
        
        SET @SQL = @SQL + '(' + @SelectColumns + ') AS ValueRow FROM ' + @TableName + ' ORDER BY id;';
        
        -- 输出生成的SQL用于调试
        PRINT '-- 生成的查询SQL:';
        PRINT SUBSTRING(@SQL, 1, 500) + CASE WHEN LEN(@SQL) > 500 THEN '...' ELSE '' END;
        PRINT '';
        
        -- 输出MySQL脚本文件信息
        PRINT '-- 文件名: ' + @FileName;
        PRINT '-- 内容预览:';
        PRINT SUBSTRING(@MySqlScript, 1, 200) + '...';
        PRINT '';
        PRINT '-- 请手动执行以下步骤:';
        PRINT '-- 1. 在SQL Server中执行上面的查询SQL获取数据';
        PRINT '-- 2. 将结果格式化为 (value1, value2, ...), 的格式';
        PRINT '-- 3. 添加到INSERT IGNORE语句中';
        PRINT '-- 4. 保存为 ' + @FileName + ' 文件';
        PRINT '-- 5. 在MySQL中执行该文件';
        PRINT '';
        
    END
    ELSE
    BEGIN
        PRINT '跳过空表: ' + @TableName;
    END
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '======================================================';
PRINT '脚本生成完成！';
PRINT '每个表都会生成对应的MySQL插入脚本文件';
PRINT '文件命名格式: [表名]_mysql_insert.sql';
PRINT '======================================================';
GO
