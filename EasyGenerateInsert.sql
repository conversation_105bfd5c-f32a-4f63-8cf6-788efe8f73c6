-- 简单实用版本：生成三个表的INSERT语句
-- 在SQL Server查询分析器中执行此脚本

-- ========================================
-- 表: bn_Attachment
-- ========================================

-- 第一步：生成INSERT语句头部
SELECT 'INSERT INTO bn_Attachment (' + 
    STUFF((
        SELECT ', ' + COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'bn_Attachment'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 2, '') + ') VALUES'

-- 第二步：生成VALUES部分
SELECT 
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = 1 THEN '('
        ELSE ',('
    END +
    STUFF((
        SELECT ', ' + 
            CASE 
                WHEN c.DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                    CASE WHEN v.value IS NULL THEN 'NULL' 
                         ELSE '''' + REPLACE(CAST(v.value AS NVARCHAR(MAX)), '''', '''''') + '''' 
                    END
                WHEN c.DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
                    CASE WHEN v.value IS NULL THEN 'NULL' 
                         ELSE '''' + CONVERT(VARCHAR(19), CAST(v.value AS DATETIME), 120) + '''' 
                    END
                WHEN c.DATA_TYPE = 'date' THEN
                    CASE WHEN v.value IS NULL THEN 'NULL' 
                         ELSE '''' + CONVERT(VARCHAR(10), CAST(v.value AS DATE), 120) + '''' 
                    END
                WHEN c.DATA_TYPE = 'bit' THEN
                    CASE WHEN v.value IS NULL THEN 'NULL' 
                         ELSE CAST(v.value AS VARCHAR(1)) 
                    END
                ELSE 
                    CASE WHEN v.value IS NULL THEN 'NULL' 
                         ELSE CAST(v.value AS NVARCHAR(MAX)) 
                    END
            END
        FROM INFORMATION_SCHEMA.COLUMNS c
        CROSS APPLY (
            SELECT 
                CASE c.COLUMN_NAME
                    WHEN (SELECT TOP 1 COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'bn_Attachment' ORDER BY ORDINAL_POSITION) THEN 
                        (SELECT TOP 1 CAST(COLUMN_DEFAULT AS SQL_VARIANT) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'bn_Attachment' ORDER BY ORDINAL_POSITION)
                    -- 这里需要根据实际列名动态获取值，简化处理
                END AS value
        ) v
        WHERE c.TABLE_NAME = 'bn_Attachment'
        ORDER BY c.ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 2, '') + 
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = (SELECT COUNT(*) FROM bn_Attachment) THEN ')'
        ELSE ')'
    END AS InsertValues
FROM bn_Attachment
WHERE EXISTS (SELECT 1 FROM bn_Attachment)

SELECT 'ON DUPLICATE KEY UPDATE Id = Id;'
SELECT ''

-- ========================================
-- 更简单的方法：直接使用具体的SELECT语句
-- ========================================

PRINT '-- ========================================'
PRINT '-- 请复制以下查询语句分别执行：'
PRINT '-- ========================================'
PRINT ''

-- bn_Attachment 表
PRINT '-- 1. bn_Attachment 表：'
PRINT 'SELECT ''INSERT INTO bn_Attachment ('' + '
SELECT '    ''' + STUFF((
        SELECT '', '' + COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'bn_Attachment'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 2, '') + '') VALUES'''

PRINT 'UNION ALL'
PRINT 'SELECT '
PRINT '    CASE WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = 1 THEN ''('' ELSE '','' + CHAR(13) + CHAR(10) + ''('' END +'

-- 动态生成列的转换逻辑
DECLARE @sql NVARCHAR(MAX) = ''
SELECT @sql = @sql + 
    CASE 
        WHEN ORDINAL_POSITION > 1 THEN ' + '', '' + '
        ELSE ''
    END +
    CASE 
        WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''' END'
        WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''' END'
        WHEN DATA_TYPE = 'date' THEN
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''' END'
        WHEN DATA_TYPE = 'bit' THEN
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
        ELSE 
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
    END
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'bn_Attachment'
ORDER BY ORDINAL_POSITION

PRINT '    ' + @sql + ' +'
PRINT '    '')'' +'
PRINT '    CASE WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = (SELECT COUNT(*) FROM bn_Attachment) THEN '''' ELSE '''' END'
PRINT 'FROM bn_Attachment'
PRINT 'UNION ALL'
PRINT 'SELECT ''ON DUPLICATE KEY UPDATE Id = Id;'''
PRINT ''

-- SysUser 表
PRINT '-- 2. SysUser 表：'
PRINT 'SELECT ''INSERT INTO SysUser ('' + '
SELECT '    ''' + STUFF((
        SELECT '', '' + COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'SysUser'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 2, '') + '') VALUES'''

PRINT 'UNION ALL'
PRINT 'SELECT '
PRINT '    CASE WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = 1 THEN ''('' ELSE '','' + CHAR(13) + CHAR(10) + ''('' END +'

SET @sql = ''
SELECT @sql = @sql + 
    CASE 
        WHEN ORDINAL_POSITION > 1 THEN ' + '', '' + '
        ELSE ''
    END +
    CASE 
        WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''' END'
        WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''' END'
        WHEN DATA_TYPE = 'date' THEN
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''' END'
        WHEN DATA_TYPE = 'bit' THEN
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
        ELSE 
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
    END
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'SysUser'
ORDER BY ORDINAL_POSITION

PRINT '    ' + @sql + ' +'
PRINT '    '')'' +'
PRINT '    CASE WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = (SELECT COUNT(*) FROM SysUser) THEN '''' ELSE '''' END'
PRINT 'FROM SysUser'
PRINT 'UNION ALL'
PRINT 'SELECT ''ON DUPLICATE KEY UPDATE Id = Id;'''
PRINT ''

-- SysUserBelong 表
PRINT '-- 3. SysUserBelong 表：'
PRINT 'SELECT ''INSERT INTO SysUserBelong ('' + '
SELECT '    ''' + STUFF((
        SELECT '', '' + COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'SysUserBelong'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 2, '') + '') VALUES'''

PRINT 'UNION ALL'
PRINT 'SELECT '
PRINT '    CASE WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = 1 THEN ''('' ELSE '','' + CHAR(13) + CHAR(10) + ''('' END +'

SET @sql = ''
SELECT @sql = @sql + 
    CASE 
        WHEN ORDINAL_POSITION > 1 THEN ' + '', '' + '
        ELSE ''
    END +
    CASE 
        WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''' END'
        WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''' END'
        WHEN DATA_TYPE = 'date' THEN
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''' END'
        WHEN DATA_TYPE = 'bit' THEN
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
        ELSE 
            'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
    END
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'SysUserBelong'
ORDER BY ORDINAL_POSITION

PRINT '    ' + @sql + ' +'
PRINT '    '')'' +'
PRINT '    CASE WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = (SELECT COUNT(*) FROM SysUserBelong) THEN '''' ELSE '''' END'
PRINT 'FROM SysUserBelong'
PRINT 'UNION ALL'
PRINT 'SELECT ''ON DUPLICATE KEY UPDATE Id = Id;'''

PRINT ''
PRINT '-- 使用说明：'
PRINT '-- 1. 复制上面输出的每个SELECT语句'
PRINT '-- 2. 在新的查询窗口中分别执行'
PRINT '-- 3. 复制执行结果即为最终的INSERT语句'
