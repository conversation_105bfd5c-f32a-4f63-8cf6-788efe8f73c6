-- 灵活的数据迁移脚本：从Hyun数据库迁移数据到HyunCoreBase数据库
-- 自动检测表结构并处理用户ID和单位ID的替换
-- 固定用户ID: 592123693924485
-- 固定单位ID: 592123124052101

USE HyunCoreBase;
GO

-- 声明变量
DECLARE @FixedUserId NVARCHAR(50) = '592123693924485';
DECLARE @FixedUnitId NVARCHAR(50) = '592123124052101';
DECLARE @TableName NVARCHAR(128);
DECLARE @SQL NVARCHAR(MAX);
DECLARE @ColumnList NVARCHAR(MAX);
DECLARE @SelectList NVARCHAR(MAX);
DECLARE @UpdateList NVARCHAR(MAX);
DECLARE @RowCount INT;

PRINT '开始灵活数据迁移...';

-- 要迁移的表列表
DECLARE table_cursor CURSOR FOR
SELECT 'dc_PurchaseList' AS TableName
UNION ALL
SELECT 'dc_PurchaseOrder'
UNION ALL
SELECT 'dc_PurchaseApproval';

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '正在处理表: ' + @TableName;
    
    -- 检查源表是否存在
    IF EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName)
    BEGIN
        -- 检查目标表是否存在
        IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName)
        BEGIN
            -- 获取表的列信息，排除Identity列
            SELECT @ColumnList = STRING_AGG(COLUMN_NAME, ', '),
                   @SelectList = STRING_AGG(
                       CASE 
                           WHEN COLUMN_NAME LIKE '%UserId' OR COLUMN_NAME IN ('CreateUserId', 'UpdateUserId', 'CreatedBy', 'UpdatedBy') 
                           THEN '''' + @FixedUserId + ''' AS ' + COLUMN_NAME
                           WHEN COLUMN_NAME LIKE '%UnitId' OR COLUMN_NAME IN ('OrganizationId', 'DepartmentId')
                           THEN '''' + @FixedUnitId + ''' AS ' + COLUMN_NAME
                           ELSE COLUMN_NAME
                       END, ', '),
                   @UpdateList = STRING_AGG(
                       COLUMN_NAME + ' = source.' + COLUMN_NAME, ', ')
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = @TableName 
              AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
              AND TABLE_SCHEMA = 'dbo';

            -- 构建MERGE语句
            SET @SQL = '
            MERGE ' + @TableName + ' AS target
            USING (
                SELECT ' + @SelectList + '
                FROM Hyun.dbo.' + @TableName + '
            ) AS source ON target.Id = source.Id
            WHEN MATCHED THEN
                UPDATE SET ' + @UpdateList + '
            WHEN NOT MATCHED THEN
                INSERT (' + @ColumnList + ')
                VALUES (' + @ColumnList + ');';

            -- 执行迁移
            BEGIN TRY
                EXEC sp_executesql @SQL;
                SET @RowCount = @@ROWCOUNT;
                PRINT @TableName + ' 表迁移完成，影响行数: ' + CAST(@RowCount AS NVARCHAR(10));
            END TRY
            BEGIN CATCH
                PRINT '迁移表 ' + @TableName + ' 时发生错误:';
                PRINT ERROR_MESSAGE();
                
                -- 尝试简单的INSERT方式
                PRINT '尝试使用INSERT方式迁移 ' + @TableName + '...';
                
                SET @SQL = '
                INSERT INTO ' + @TableName + ' (' + @ColumnList + ')
                SELECT ' + @SelectList + '
                FROM Hyun.dbo.' + @TableName + ' source
                WHERE NOT EXISTS (
                    SELECT 1 FROM ' + @TableName + ' target 
                    WHERE target.Id = source.Id
                );';
                
                BEGIN TRY
                    EXEC sp_executesql @SQL;
                    SET @RowCount = @@ROWCOUNT;
                    PRINT @TableName + ' 表INSERT迁移完成，新增行数: ' + CAST(@RowCount AS NVARCHAR(10));
                END TRY
                BEGIN CATCH
                    PRINT '使用INSERT方式迁移 ' + @TableName + ' 也失败:';
                    PRINT ERROR_MESSAGE();
                END CATCH
            END CATCH
        END
        ELSE
        BEGIN
            PRINT '目标表 ' + @TableName + ' 不存在，跳过迁移';
        END
    END
    ELSE
    BEGIN
        PRINT '源表 ' + @TableName + ' 不存在，跳过迁移';
    END
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '所有数据迁移完成！';
GO
