-- 生成完整的MySQL脚本
-- 每个表输出完整的INSERT语句，可直接保存为.sql文件

USE syjx;
GO

-- 声明变量
DECLARE @TableName NVARCHAR(128);
DECLARE @RowCount INT;

PRINT '-- ======================================================';
PRINT '-- 自动生成的MySQL导入脚本集合';
PRINT '-- 生成时间: ' + CONVERT(VARCHAR(19), GETDATE(), 120);
PRINT '-- 数据库: syjx (SQL Server) -> syjx (MySQL)';
PRINT '-- ======================================================';
PRINT '';

-- 获取所有有id字段且有数据的表
DECLARE table_cursor CURSOR FOR
SELECT t.TABLE_NAME
FROM INFORMATION_SCHEMA.TABLES t
WHERE t.TABLE_TYPE = 'BASE TABLE'
AND EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS c
    WHERE c.TABLE_NAME = t.TABLE_NAME 
    AND c.COLUMN_NAME = 'id'
    AND c.DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint')
)
ORDER BY t.TABLE_NAME;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    -- 检查表是否有数据
    DECLARE @SQL NVARCHAR(MAX);
    SET @SQL = 'SELECT @count = COUNT(*) FROM ' + @TableName;
    EXEC sp_executesql @SQL, N'@count INT OUTPUT', @count = @RowCount OUTPUT;
    
    IF @RowCount > 0
    BEGIN
        PRINT '-- ======================================================';
        PRINT '-- 文件: ' + @TableName + '_mysql_insert.sql';
        PRINT '-- 表: ' + @TableName + ' (记录数: ' + CAST(@RowCount AS VARCHAR(10)) + ')';
        PRINT '-- ======================================================';
        PRINT '';
        PRINT '-- MySQL插入脚本 - 表: ' + @TableName;
        PRINT '-- 生成时间: ' + CONVERT(VARCHAR(19), GETDATE(), 120);
        PRINT '-- 记录数: ' + CAST(@RowCount AS VARCHAR(10));
        PRINT '-- 说明: 使用INSERT IGNORE自动跳过重复的id';
        PRINT '';
        
        -- 获取列名列表
        DECLARE @ColumnList NVARCHAR(MAX) = '';
        DECLARE @ColumnName NVARCHAR(128);
        
        DECLARE column_cursor CURSOR FOR
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @TableName
        ORDER BY ORDINAL_POSITION;
        
        OPEN column_cursor;
        FETCH NEXT FROM column_cursor INTO @ColumnName;
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            IF @ColumnList != ''
                SET @ColumnList = @ColumnList + ', ';
            SET @ColumnList = @ColumnList + '`' + @ColumnName + '`';
            
            FETCH NEXT FROM column_cursor INTO @ColumnName;
        END
        
        CLOSE column_cursor;
        DEALLOCATE column_cursor;
        
        -- 输出INSERT语句头部
        PRINT 'INSERT IGNORE INTO `' + @TableName + '` (' + @ColumnList + ') VALUES';
        
        -- 生成数据查询语句供手动执行
        PRINT '';
        PRINT '-- 请在SQL Server中执行以下查询获取数据:';
        PRINT 'SELECT ';
        
        -- 生成SELECT列表
        DECLARE @SelectColumns NVARCHAR(MAX) = '';
        DECLARE @DataType NVARCHAR(128);
        DECLARE @IsFirstCol BIT = 1;
        
        DECLARE select_cursor CURSOR FOR
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @TableName
        ORDER BY ORDINAL_POSITION;
        
        OPEN select_cursor;
        FETCH NEXT FROM select_cursor INTO @ColumnName, @DataType;
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            IF @IsFirstCol = 0
                PRINT ',';
            ELSE
                SET @IsFirstCol = 0;
                
            -- 根据数据类型生成相应的转换
            DECLARE @ColumnExpression NVARCHAR(500);
            SET @ColumnExpression = 
                CASE 
                    WHEN @DataType IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                        'CASE WHEN ' + @ColumnName + ' IS NULL THEN ''NULL'' ELSE '''''''' + REPLACE(REPLACE(REPLACE(' + @ColumnName + ', '''''''', ''''''''''''), CHAR(13), '' ''), CHAR(10), '' '') + '''''''' END'
                    WHEN @DataType IN ('datetime', 'datetime2', 'smalldatetime') THEN
                        'CASE WHEN ' + @ColumnName + ' IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(19), ' + @ColumnName + ', 120) + '''''''' END'
                    WHEN @DataType IN ('date') THEN
                        'CASE WHEN ' + @ColumnName + ' IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(10), ' + @ColumnName + ', 120) + '''''''' END'
                    WHEN @DataType IN ('time') THEN
                        'CASE WHEN ' + @ColumnName + ' IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(8), ' + @ColumnName + ', 108) + '''''''' END'
                    WHEN @DataType IN ('bit') THEN
                        'CAST(ISNULL(' + @ColumnName + ', 0) AS VARCHAR(1))'
                    WHEN @DataType IN ('decimal', 'numeric', 'money', 'smallmoney', 'float', 'real') THEN
                        'CASE WHEN ' + @ColumnName + ' IS NULL THEN ''NULL'' ELSE CAST(' + @ColumnName + ' AS VARCHAR(50)) END'
                    ELSE
                        'CASE WHEN ' + @ColumnName + ' IS NULL THEN ''NULL'' ELSE CAST(' + @ColumnName + ' AS VARCHAR(MAX)) END'
                END;
            
            PRINT '    ' + @ColumnExpression + ' AS ' + @ColumnName;
            
            FETCH NEXT FROM select_cursor INTO @ColumnName, @DataType;
        END
        
        CLOSE select_cursor;
        DEALLOCATE select_cursor;
        
        PRINT 'FROM ' + @TableName + ' ORDER BY id;';
        PRINT '';
        PRINT '-- 将上面查询的结果格式化为以下格式:';
        PRINT '-- (value1, value2, value3, ...),';
        PRINT '-- (value1, value2, value3, ...),';
        PRINT '-- (value1, value2, value3, ...)  -- 最后一行不要逗号';
        PRINT ';';
        PRINT '';
        PRINT '-- 完整示例:';
        PRINT '-- INSERT IGNORE INTO `' + @TableName + '` (' + @ColumnList + ') VALUES';
        PRINT '-- (1, ''示例数据'', ''2024-01-01''),';
        PRINT '-- (2, ''示例数据2'', ''2024-01-02'');';
        PRINT '';
        PRINT '-- ======================================================';
        PRINT '-- 文件结束: ' + @TableName + '_mysql_insert.sql';
        PRINT '-- ======================================================';
        PRINT '';
        PRINT '';
        
    END
    ELSE
    BEGIN
        PRINT '-- 跳过空表: ' + @TableName + ' (无数据)';
        PRINT '';
    END
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '-- ======================================================';
PRINT '-- 所有脚本生成完成！';
PRINT '-- ';
PRINT '-- 使用说明:';
PRINT '-- 1. 复制每个表对应的SELECT语句';
PRINT '-- 2. 在SQL Server中执行获取数据';
PRINT '-- 3. 将结果格式化为INSERT VALUES格式';
PRINT '-- 4. 保存为对应的.sql文件';
PRINT '-- 5. 在MySQL中执行这些文件';
PRINT '-- ';
PRINT '-- INSERT IGNORE会自动跳过重复的id，确保数据不重复';
PRINT '-- ======================================================';
GO
