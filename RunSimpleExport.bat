@echo off
echo ========================================
echo Simple SQL Server to MySQL Export
echo Compatible with SQL Server 2008+
echo ========================================
echo.
echo Connecting to: 192.168.2.3
echo Database: syjx
echo User: hyun
echo.

sqlcmd -S 192.168.2.3 -U hyun -P hyun -d syjx -i SimpleExportCompatible.sql

echo.
echo ========================================
if %ERRORLEVEL% EQU 0 (
    echo Success! 
    echo.
    echo Next Steps:
    echo 1. Copy the SELECT statements above
    echo 2. Execute in SQL Server to get data
    echo 3. Create tables in MySQL
    echo 4. Use INSERT IGNORE to import data
) else (
    echo Failed! Error: %ERRORLEVEL%
    echo Check connection and permissions
)
echo ========================================
pause
