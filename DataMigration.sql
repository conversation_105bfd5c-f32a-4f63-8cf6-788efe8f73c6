-- 数据迁移脚本：从Hyun数据库迁移数据到HyunCoreBase数据库
-- 固定用户ID: 592123693924485
-- 固定单位ID: 592123124052101

USE HyunCoreBase;
GO

-- 声明变量
DECLARE @FixedUserId NVARCHAR(50) = '592123693924485';
DECLARE @FixedUnitId NVARCHAR(50) = '592123124052101';

PRINT '开始数据迁移...';

-- 1. 迁移 dc_PurchaseList 表
PRINT '正在迁移 dc_PurchaseList 表...';

-- 使用MERGE语句进行插入或更新
MERGE dc_PurchaseList AS target
USING (
    SELECT 
        Id,
        PurchaseNo,
        PurchaseDate,
        SupplierId,
        SupplierName,
        TotalAmount,
        Status,
        @FixedUserId AS CreateUserId,
        CreateTime,
        @FixedUserId AS UpdateUserId,
        UpdateTime,
        @FixedUnitId AS UnitId,
        Remark
    FROM Hyun.dbo.dc_PurchaseList
) AS source ON target.Id = source.Id
WHEN MATCHED THEN
    UPDATE SET
        PurchaseNo = source.PurchaseNo,
        PurchaseDate = source.PurchaseDate,
        SupplierId = source.SupplierId,
        SupplierName = source.SupplierName,
        TotalAmount = source.TotalAmount,
        Status = source.Status,
        CreateUserId = source.CreateUserId,
        CreateTime = source.CreateTime,
        UpdateUserId = source.UpdateUserId,
        UpdateTime = source.UpdateTime,
        UnitId = source.UnitId,
        Remark = source.Remark
WHEN NOT MATCHED THEN
    INSERT (Id, PurchaseNo, PurchaseDate, SupplierId, SupplierName, TotalAmount, Status, 
            CreateUserId, CreateTime, UpdateUserId, UpdateTime, UnitId, Remark)
    VALUES (source.Id, source.PurchaseNo, source.PurchaseDate, source.SupplierId, 
            source.SupplierName, source.TotalAmount, source.Status, source.CreateUserId, 
            source.CreateTime, source.UpdateUserId, source.UpdateTime, source.UnitId, source.Remark);

PRINT 'dc_PurchaseList 表迁移完成，影响行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- 2. 迁移 dc_PurchaseOrder 表
PRINT '正在迁移 dc_PurchaseOrder 表...';

MERGE dc_PurchaseOrder AS target
USING (
    SELECT 
        Id,
        OrderNo,
        OrderDate,
        SupplierId,
        SupplierName,
        TotalAmount,
        Status,
        @FixedUserId AS CreateUserId,
        CreateTime,
        @FixedUserId AS UpdateUserId,
        UpdateTime,
        @FixedUnitId AS UnitId,
        Remark,
        PurchaseListId
    FROM Hyun.dbo.dc_PurchaseOrder
) AS source ON target.Id = source.Id
WHEN MATCHED THEN
    UPDATE SET
        OrderNo = source.OrderNo,
        OrderDate = source.OrderDate,
        SupplierId = source.SupplierId,
        SupplierName = source.SupplierName,
        TotalAmount = source.TotalAmount,
        Status = source.Status,
        CreateUserId = source.CreateUserId,
        CreateTime = source.CreateTime,
        UpdateUserId = source.UpdateUserId,
        UpdateTime = source.UpdateTime,
        UnitId = source.UnitId,
        Remark = source.Remark,
        PurchaseListId = source.PurchaseListId
WHEN NOT MATCHED THEN
    INSERT (Id, OrderNo, OrderDate, SupplierId, SupplierName, TotalAmount, Status, 
            CreateUserId, CreateTime, UpdateUserId, UpdateTime, UnitId, Remark, PurchaseListId)
    VALUES (source.Id, source.OrderNo, source.OrderDate, source.SupplierId, 
            source.SupplierName, source.TotalAmount, source.Status, source.CreateUserId, 
            source.CreateTime, source.UpdateUserId, source.UpdateTime, source.UnitId, 
            source.Remark, source.PurchaseListId);

PRINT 'dc_PurchaseOrder 表迁移完成，影响行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

-- 3. 迁移 dc_PurchaseApproval 表
PRINT '正在迁移 dc_PurchaseApproval 表...';

MERGE dc_PurchaseApproval AS target
USING (
    SELECT 
        Id,
        ApprovalNo,
        ApprovalDate,
        PurchaseOrderId,
        ApprovalStatus,
        ApprovalComment,
        @FixedUserId AS CreateUserId,
        CreateTime,
        @FixedUserId AS UpdateUserId,
        UpdateTime,
        @FixedUnitId AS UnitId,
        Remark
    FROM Hyun.dbo.dc_PurchaseApproval
) AS source ON target.Id = source.Id
WHEN MATCHED THEN
    UPDATE SET
        ApprovalNo = source.ApprovalNo,
        ApprovalDate = source.ApprovalDate,
        PurchaseOrderId = source.PurchaseOrderId,
        ApprovalStatus = source.ApprovalStatus,
        ApprovalComment = source.ApprovalComment,
        CreateUserId = source.CreateUserId,
        CreateTime = source.CreateTime,
        UpdateUserId = source.UpdateUserId,
        UpdateTime = source.UpdateTime,
        UnitId = source.UnitId,
        Remark = source.Remark
WHEN NOT MATCHED THEN
    INSERT (Id, ApprovalNo, ApprovalDate, PurchaseOrderId, ApprovalStatus, ApprovalComment,
            CreateUserId, CreateTime, UpdateUserId, UpdateTime, UnitId, Remark)
    VALUES (source.Id, source.ApprovalNo, source.ApprovalDate, source.PurchaseOrderId, 
            source.ApprovalStatus, source.ApprovalComment, source.CreateUserId, 
            source.CreateTime, source.UpdateUserId, source.UpdateTime, source.UnitId, source.Remark);

PRINT 'dc_PurchaseApproval 表迁移完成，影响行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));

PRINT '所有数据迁移完成！';
GO
