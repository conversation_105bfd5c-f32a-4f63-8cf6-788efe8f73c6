@echo off
chcp 65001 >nul
echo ========================================
echo 数据迁移工具 - INSERT INTO SELECT方式
echo ========================================
echo.
echo 服务器: 192.168.2.3
echo 源数据库: Hyun
echo 目标数据库: HyunCoreBase
echo 固定用户ID: 592123693924485
echo 固定单位ID: 592123124052101
echo.

:menu
echo 请选择要执行的脚本：
echo 1. 生成所有dc表迁移脚本-正确版 (GenerateDcTablesMigration_Correct.sql) - 推荐
echo 2. 执行所有dc表迁移 (ExecuteDcTablesMigration.sql) - 自动迁移
echo 3. 测试逗号修复 (TestCommaFix.sql) - 调试用
echo 4. 显示表结构对比 (ShowTableStructure.sql)
echo 5. 检查并生成脚本 (InspectAndGenerate.sql)
echo 6. 分析表字段差异 (AnalyzeTableDifferences.sql)
echo 7. 退出
echo.
echo 推荐流程：
echo   选择1生成脚本（正确版本），逻辑：源表存在的字段直接同步，不存在的用默认值
echo   复制生成的SQL到SSMS中执行，如有报错手动调整
echo.
set /p choice=请输入选择 (1-7):

if "%choice%"=="1" goto generatedctablesfixed
if "%choice%"=="2" goto executedctables
if "%choice%"=="3" goto testcomma
if "%choice%"=="4" goto showstructure
if "%choice%"=="5" goto inspect
if "%choice%"=="6" goto analyze
if "%choice%"=="7" goto exit
echo 无效选择，请重新输入
goto menu

:generatedctablesfixed
echo.
echo 正在生成所有dc表的迁移脚本（正确版）...
echo 逻辑：源表存在的字段直接同步，源表不存在的字段用默认值
echo 特殊处理：SchoolId/UnitId强制使用固定值，UserId相关字段强制使用固定值
sqlcmd -S 192.168.2.3 -U hyun -P hyun -i GenerateDcTablesMigration_Correct.sql
goto result

:testcomma
echo.
echo 正在测试逗号修复...
echo 此工具用于验证生成的SQL语句逗号格式是否正确
sqlcmd -S 192.168.2.3 -U hyun -P hyun -i TestCommaFix.sql
goto result

:executedctables
echo.
echo 正在自动执行所有dc表的迁移...
echo 注意：如果某些表失败，请手动调整字段映射
sqlcmd -S 192.168.2.3 -U hyun -P hyun -i ExecuteDcTablesMigration.sql
goto result

:showstructure
echo.
echo 正在显示表结构对比...
echo 此工具将显示源表和目标表的实际字段结构
sqlcmd -S 192.168.2.3 -U hyun -P hyun -i ShowTableStructure.sql
goto result

:inspect
echo.
echo 正在检查表结构并生成脚本...
echo 此工具将分析表结构并生成INSERT脚本模板
sqlcmd -S 192.168.2.3 -U hyun -P hyun -i InspectAndGenerate.sql
goto result

:fieldmapping
echo.
echo 正在执行智能字段映射迁移...
echo 此脚本会动态分析表结构并生成正确的INSERT语句
sqlcmd -S 192.168.2.3 -U hyun -P hyun -i FieldMappingMigration.sql
goto result

:analyze
echo.
echo 正在分析表字段差异...
echo 此工具将显示源表和目标表的字段对比
sqlcmd -S 192.168.2.3 -U hyun -P hyun -i AnalyzeTableDifferences.sql
goto result

:result
echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo 执行完成！
    echo ========================================
) else (
    echo ========================================
    echo 执行失败！错误代码: %ERRORLEVEL%
    echo ========================================
)
echo.
echo 按任意键返回菜单...
pause >nul
goto menu

:exit
echo 再见！
pause
