-- 生成所有dc开头表的迁移脚本
-- 按照用户思路：先写INSERT列出所有字段，再写SELECT对应字段，报错就改默认值

USE HyunCoreBase;
GO

DECLARE @userid BIGINT = 592123693924485;
DECLARE @unitId BIGINT = 592123124052101;

PRINT '========================================';
PRINT '生成dc开头表的迁移脚本';
PRINT '========================================';
PRINT '用户ID: ' + CAST(@userid AS NVARCHAR(20));
PRINT '单位ID: ' + CAST(@unitId AS NVARCHAR(20));
PRINT '';

-- 获取所有以dc开头的表
DECLARE @TableName NVARCHAR(128);
DECLARE @SQL NVARCHAR(MAX);
DECLARE @InsertColumns NVARCHAR(MAX);
DECLARE @SelectColumns NVARCHAR(MAX);

DECLARE table_cursor CURSOR FOR
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME LIKE 'dc%' 
  AND TABLE_TYPE = 'BASE TABLE'
  AND TABLE_SCHEMA = 'dbo'
ORDER BY TABLE_NAME;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '========================================';
    PRINT '-- ' + @TableName + ' 表迁移脚本';
    PRINT '========================================';
    
    -- 检查源表是否存在
    IF EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo')
    BEGIN
        PRINT 'PRINT ''正在迁移 ' + @TableName + ' 表...'';';
        PRINT '';
        PRINT 'BEGIN TRY';
        
        -- 生成INSERT字段列表
        SET @InsertColumns = '';
        SELECT @InsertColumns = @InsertColumns +
            CASE WHEN @InsertColumns = '' THEN '' ELSE ',' + CHAR(13) + '          ' END +
            COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @TableName
          AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
          AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;
        
        -- 生成SELECT字段列表
        SET @SelectColumns = '';
        SELECT @SelectColumns = @SelectColumns +
            CASE WHEN @SelectColumns = '' THEN '' ELSE ',' + CHAR(13) + '          ' END +
            CASE
                -- SchoolId 和 UnitId 字段统一使用固定单位ID
                WHEN COLUMN_NAME IN ('SchoolId', 'UnitId', 'OrganizationId', 'DepartmentId')
                THEN CAST(@unitId AS NVARCHAR(20)) + ' AS ' + COLUMN_NAME + ' -- 固定单位ID'

                -- 用户ID相关字段使用固定用户ID
                WHEN COLUMN_NAME LIKE '%UserId' OR COLUMN_NAME IN ('CreateUserId', 'UpdateUserId', 'CreatedBy', 'UpdatedBy', 'CreateId', 'ModifyId', 'CreateBy', 'ModifyBy')
                THEN CAST(@userid AS NVARCHAR(20)) + ' AS ' + COLUMN_NAME + ' -- 固定用户ID'

                -- 时间字段如果是Create/Modify相关，使用GETDATE()
                WHEN COLUMN_NAME IN ('CreateTime', 'ModifyTime', 'UpdateTime', 'RegDate')
                THEN 'GETDATE() AS ' + COLUMN_NAME + ' -- 当前时间'

                -- 删除标记字段默认为0
                WHEN COLUMN_NAME IN ('IsDeleted', 'Deleted', 'IsDelete')
                THEN '0 AS ' + COLUMN_NAME + ' -- 未删除'

                -- 版本字段默认为0
                WHEN COLUMN_NAME IN ('Version', 'VersionNo')
                THEN '0 AS ' + COLUMN_NAME + ' -- 版本号'

                -- 状态字段默认为1
                WHEN COLUMN_NAME IN ('Status', 'Statuz', 'State')
                THEN '1 AS ' + COLUMN_NAME + ' -- 状态'

                -- 其他字段直接使用源表字段（如果报错再改默认值）
                ELSE 'src.' + COLUMN_NAME
            END
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @TableName
          AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
          AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;
        
        -- 生成完整的INSERT语句
        PRINT '    INSERT INTO dbo.' + @TableName;
        PRINT '    (';
        PRINT '          ' + @InsertColumns;
        PRINT '    )';
        PRINT '    SELECT';
        PRINT '          ' + @SelectColumns;
        PRINT '    FROM Hyun.dbo.' + @TableName + ' src';
        PRINT '    WHERE NOT EXISTS (';
        PRINT '        SELECT 1 FROM ' + @TableName + ' target';
        PRINT '        WHERE target.Id = src.Id';
        PRINT '    );';
        PRINT '';
        PRINT '    PRINT ''' + @TableName + ' 表迁移完成，新增行数: '' + CAST(@@ROWCOUNT AS NVARCHAR(10));';
        PRINT 'END TRY';
        PRINT 'BEGIN CATCH';
        PRINT '    PRINT ''' + @TableName + ' 表迁移失败: '' + ERROR_MESSAGE();';
        PRINT '    PRINT ''请根据错误信息调整字段映射，把报错字段改为默认值'';';
        PRINT 'END CATCH';
        PRINT '';
        PRINT 'PRINT '''';';
        PRINT '';
    END
    ELSE
    BEGIN
        PRINT '-- 源表 Hyun.dbo.' + @TableName + ' 不存在，跳过';
        PRINT '';
    END
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '========================================';
PRINT '-- 脚本生成完成！';
PRINT '========================================';
PRINT '';
PRINT '/*';
PRINT '使用说明：';
PRINT '1. 复制上面生成的INSERT语句到新查询窗口';
PRINT '2. 执行脚本，如果报错"列名无效"或"字段不存在"';
PRINT '3. 就把报错的字段替换成对应的默认值：';
PRINT '   - 数值类型: 0';
PRINT '   - 字符串类型: ''''';
PRINT '   - 日期类型: GETDATE()';
PRINT '   - 布尔类型: 0 或 1';
PRINT '   - GUID类型: NEWID()';
PRINT '';
PRINT '常用默认值示例：';
PRINT '   0 AS SomeIntField,           -- 数值字段';
PRINT '   '''' AS SomeStringField,       -- 字符串字段';
PRINT '   GETDATE() AS SomeDateField,  -- 日期字段';
PRINT '   NEWID() AS SomeGuidField,    -- GUID字段';
PRINT '   0 AS IsDeleted,              -- 删除标记';
PRINT '   1 AS Status                  -- 状态字段';
PRINT '';
PRINT '固定值：';
PRINT '   SchoolId/UnitId: ' + CAST(@unitId AS NVARCHAR(20));
PRINT '   UserId相关: ' + CAST(@userid AS NVARCHAR(20));
PRINT '*/';

GO
