-- 简化的INSERT INTO SELECT数据迁移脚本
-- 从Hyun数据库迁移数据到HyunCoreBase数据库
-- 固定用户ID: 592123693924485
-- 固定单位ID: 592123124052101

USE HyunCoreBase;
GO

DECLARE @FixedUserId NVARCHAR(50) = '592123693924485';
DECLARE @FixedUnitId NVARCHAR(50) = '592123124052101';

PRINT '开始数据迁移...';
PRINT '固定用户ID: ' + @FixedUserId;
PRINT '固定单位ID: ' + @FixedUnitId;
PRINT '';

-- ========================================
-- 1. 迁移 dc_PurchaseList 表
-- ========================================
PRINT '正在迁移 dc_PurchaseList 表...';

BEGIN TRY
    -- 动态构建INSERT语句，只使用目标表实际存在的字段
    DECLARE @SQL1 NVARCHAR(MAX);
    DECLARE @Columns1 NVARCHAR(MAX);
    DECLARE @Values1 NVARCHAR(MAX);

    -- 获取目标表的列信息
    SELECT @Columns1 = STRING_AGG(COLUMN_NAME, ', '),
           @Values1 = STRING_AGG(
               CASE
                   -- 用户ID字段使用固定值
                   WHEN COLUMN_NAME LIKE '%UserId' OR COLUMN_NAME IN ('CreateUserId', 'UpdateUserId', 'CreatedBy', 'UpdatedBy')
                   THEN '''' + @FixedUserId + ''''

                   -- 单位ID字段使用固定值
                   WHEN COLUMN_NAME LIKE '%UnitId' OR COLUMN_NAME IN ('OrganizationId', 'DepartmentId')
                   THEN '''' + @FixedUnitId + ''''

                   -- 检查源表是否有此字段
                   WHEN EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.COLUMNS
                               WHERE TABLE_NAME = 'dc_PurchaseList' AND COLUMN_NAME = c.COLUMN_NAME)
                   THEN 'ISNULL(src.' + COLUMN_NAME + ', ' +
                        CASE
                            WHEN DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint', 'bit') THEN '0'
                            WHEN DATA_TYPE IN ('decimal', 'numeric', 'float', 'real', 'money', 'smallmoney') THEN '0.0'
                            WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN ''''''
                            WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime', 'date', 'time') THEN 'GETDATE()'
                            WHEN DATA_TYPE = 'uniqueidentifier' THEN 'NEWID()'
                            ELSE ''''''
                        END + ')'

                   -- 源表没有此字段，使用默认值
                   ELSE
                        CASE
                            WHEN DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint', 'bit') THEN '0'
                            WHEN DATA_TYPE IN ('decimal', 'numeric', 'float', 'real', 'money', 'smallmoney') THEN '0.0'
                            WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN ''''''
                            WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime', 'date', 'time') THEN 'GETDATE()'
                            WHEN DATA_TYPE = 'uniqueidentifier' THEN 'NEWID()'
                            ELSE ''''''
                        END
               END, ', ')
    FROM INFORMATION_SCHEMA.COLUMNS c
    WHERE TABLE_NAME = 'dc_PurchaseList'
      AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
      AND TABLE_SCHEMA = 'dbo'
    ORDER BY ORDINAL_POSITION;

    SET @SQL1 = 'INSERT INTO dc_PurchaseList (' + @Columns1 + ') ' +
                'SELECT ' + @Values1 + ' ' +
                'FROM Hyun.dbo.dc_PurchaseList src ' +
                'WHERE NOT EXISTS (SELECT 1 FROM dc_PurchaseList target WHERE target.Id = src.Id)';

    EXEC sp_executesql @SQL1;
    PRINT 'dc_PurchaseList 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseList 表迁移失败: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- ========================================
-- 2. 迁移 dc_PurchaseOrder 表
-- ========================================
PRINT '正在迁移 dc_PurchaseOrder 表...';

BEGIN TRY
    INSERT INTO dc_PurchaseOrder (
        Id,
        OrderNo,
        OrderDate,
        SupplierId,
        SupplierName,
        TotalAmount,
        Status,
        CreateUserId,
        CreateTime,
        UpdateUserId,
        UpdateTime,
        UnitId,
        Remark,
        PurchaseListId
    )
    SELECT 
        ISNULL(src.Id, NEWID()) AS Id,
        ISNULL(src.OrderNo, '') AS OrderNo,
        ISNULL(src.OrderDate, GETDATE()) AS OrderDate,
        ISNULL(src.SupplierId, '') AS SupplierId,
        ISNULL(src.SupplierName, '') AS SupplierName,
        ISNULL(src.TotalAmount, 0) AS TotalAmount,
        ISNULL(src.Status, 0) AS Status,
        @FixedUserId AS CreateUserId,
        ISNULL(src.CreateTime, GETDATE()) AS CreateTime,
        @FixedUserId AS UpdateUserId,
        ISNULL(src.UpdateTime, GETDATE()) AS UpdateTime,
        @FixedUnitId AS UnitId,
        ISNULL(src.Remark, '') AS Remark,
        ISNULL(src.PurchaseListId, '') AS PurchaseListId
    FROM Hyun.dbo.dc_PurchaseOrder src
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseOrder target 
        WHERE target.Id = src.Id
    );
    
    PRINT 'dc_PurchaseOrder 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseOrder 表迁移失败: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- ========================================
-- 3. 迁移 dc_PurchaseApproval 表
-- ========================================
PRINT '正在迁移 dc_PurchaseApproval 表...';

BEGIN TRY
    INSERT INTO dc_PurchaseApproval (
        Id,
        ApprovalNo,
        ApprovalDate,
        PurchaseOrderId,
        ApprovalStatus,
        ApprovalComment,
        CreateUserId,
        CreateTime,
        UpdateUserId,
        UpdateTime,
        UnitId,
        Remark
    )
    SELECT 
        ISNULL(src.Id, NEWID()) AS Id,
        ISNULL(src.ApprovalNo, '') AS ApprovalNo,
        ISNULL(src.ApprovalDate, GETDATE()) AS ApprovalDate,
        ISNULL(src.PurchaseOrderId, '') AS PurchaseOrderId,
        ISNULL(src.ApprovalStatus, 0) AS ApprovalStatus,
        ISNULL(src.ApprovalComment, '') AS ApprovalComment,
        @FixedUserId AS CreateUserId,
        ISNULL(src.CreateTime, GETDATE()) AS CreateTime,
        @FixedUserId AS UpdateUserId,
        ISNULL(src.UpdateTime, GETDATE()) AS UpdateTime,
        @FixedUnitId AS UnitId,
        ISNULL(src.Remark, '') AS Remark
    FROM Hyun.dbo.dc_PurchaseApproval src
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseApproval target 
        WHERE target.Id = src.Id
    );
    
    PRINT 'dc_PurchaseApproval 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseApproval 表迁移失败: ' + ERROR_MESSAGE();
END CATCH

PRINT '';
PRINT '========================================';
PRINT '所有数据迁移完成！';
PRINT '========================================';

-- 显示迁移后的统计信息
PRINT '';
PRINT '迁移后统计信息:';
PRINT 'dc_PurchaseList 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseList) AS NVARCHAR(10));
PRINT 'dc_PurchaseOrder 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseOrder) AS NVARCHAR(10));
PRINT 'dc_PurchaseApproval 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseApproval) AS NVARCHAR(10));

GO
