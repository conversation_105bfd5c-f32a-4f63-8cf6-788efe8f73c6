-- 可直接运行的INSERT语句生成器
-- 为三个表生成INSERT语句，每条数据一个INSERT

-- ========================================
-- 生成 bn_Attachment 表的INSERT语句
-- ========================================

PRINT '-- =========================================='
PRINT '-- bn_Attachment 表的INSERT语句'
PRINT '-- =========================================='

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'bn_Attachment')
BEGIN
    DECLARE @sql_att NVARCHAR(MAX)
    DECLARE @columns_att NVARCHAR(MAX)

    -- 获取列名列表
    SELECT @columns_att = STUFF((
        SELECT ', ' + COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'bn_Attachment'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 2, '')

    -- 构建动态SQL
    SET @sql_att = 'SELECT ''INSERT INTO bn_Attachment (' + @columns_att + ') VALUES ('' + '

    -- 为每列添加值转换
    SELECT @sql_att = @sql_att + 
        STUFF((
            SELECT ' + '', '' + ' + 
                CASE 
                    WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''''' END'
                    WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                    WHEN DATA_TYPE = 'date' THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                    WHEN DATA_TYPE = 'bit' THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
                    ELSE 
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
                END
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'bn_Attachment'
            ORDER BY ORDINAL_POSITION
            FOR XML PATH('')
        ), 1, 7, '')

    SET @sql_att = @sql_att + ' + '') ON DUPLICATE KEY UPDATE Id = Id;'' FROM bn_Attachment'

    -- 执行动态SQL
    EXEC sp_executesql @sql_att
END
ELSE
BEGIN
    PRINT '-- 表 bn_Attachment 不存在'
END

PRINT ''
PRINT ''

-- ========================================
-- 生成 SysUser 表的INSERT语句
-- ========================================

PRINT '-- =========================================='
PRINT '-- SysUser 表的INSERT语句'
PRINT '-- =========================================='

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SysUser')
BEGIN
    DECLARE @sql_user NVARCHAR(MAX)
    DECLARE @columns_user NVARCHAR(MAX)

    -- 获取列名列表
    SELECT @columns_user = STUFF((
        SELECT ', ' + COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'SysUser'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 2, '')

    -- 构建动态SQL
    SET @sql_user = 'SELECT ''INSERT INTO SysUser (' + @columns_user + ') VALUES ('' + '

    -- 为每列添加值转换
    SELECT @sql_user = @sql_user + 
        STUFF((
            SELECT ' + '', '' + ' + 
                CASE 
                    WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''''' END'
                    WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                    WHEN DATA_TYPE = 'date' THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                    WHEN DATA_TYPE = 'bit' THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
                    ELSE 
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
                END
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'SysUser'
            ORDER BY ORDINAL_POSITION
            FOR XML PATH('')
        ), 1, 7, '')

    SET @sql_user = @sql_user + ' + '') ON DUPLICATE KEY UPDATE Id = Id;'' FROM SysUser'

    -- 执行动态SQL
    EXEC sp_executesql @sql_user
END
ELSE
BEGIN
    PRINT '-- 表 SysUser 不存在'
END

PRINT ''
PRINT ''

-- ========================================
-- 生成 SysUserBelong 表的INSERT语句
-- ========================================

PRINT '-- =========================================='
PRINT '-- SysUserBelong 表的INSERT语句'
PRINT '-- =========================================='

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SysUserBelong')
BEGIN
    DECLARE @sql_belong NVARCHAR(MAX)
    DECLARE @columns_belong NVARCHAR(MAX)

    -- 获取列名列表
    SELECT @columns_belong = STUFF((
        SELECT ', ' + COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'SysUserBelong'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 2, '')

    -- 构建动态SQL
    SET @sql_belong = 'SELECT ''INSERT INTO SysUserBelong (' + @columns_belong + ') VALUES ('' + '

    -- 为每列添加值转换
    SELECT @sql_belong = @sql_belong + 
        STUFF((
            SELECT ' + '', '' + ' + 
                CASE 
                    WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''''' END'
                    WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                    WHEN DATA_TYPE = 'date' THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                    WHEN DATA_TYPE = 'bit' THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
                    ELSE 
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
                END
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'SysUserBelong'
            ORDER BY ORDINAL_POSITION
            FOR XML PATH('')
        ), 1, 7, '')

    SET @sql_belong = @sql_belong + ' + '') ON DUPLICATE KEY UPDATE Id = Id;'' FROM SysUserBelong'

    -- 执行动态SQL
    EXEC sp_executesql @sql_belong
END
ELSE
BEGIN
    PRINT '-- 表 SysUserBelong 不存在'
END

PRINT ''
PRINT '-- =========================================='
PRINT '-- 脚本执行完成'
PRINT '-- 每条数据生成一个独立的INSERT语句'
PRINT '-- 格式：INSERT INTO 表名 (...) VALUES (...) ON DUPLICATE KEY UPDATE Id = Id;'
PRINT '-- =========================================='
