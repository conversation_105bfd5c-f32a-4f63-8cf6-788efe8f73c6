-- 执行所有dc开头表的数据迁移
-- 按照用户思路实现：INSERT INTO SELECT，报错就改默认值

USE HyunCoreBase;
GO

DECLARE @userid BIGINT = 592123693924485;
DECLARE @unitId BIGINT = 592123124052101;

PRINT '========================================';
PRINT '开始迁移所有dc开头的表';
PRINT '========================================';
PRINT '用户ID: ' + CAST(@userid AS NVARCHAR(20));
PRINT '单位ID: ' + CAST(@unitId AS NVARCHAR(20));
PRINT '时间: ' + CONVERT(NVARCHAR(20), GETDATE(), 120);
PRINT '';

-- 动态生成并执行迁移脚本
DECLARE @TableName NVARCHAR(128);
DECLARE @SQL NVARCHAR(MAX);
DECLARE @InsertColumns NVARCHAR(MAX);
DECLARE @SelectColumns NVARCHAR(MAX);
DECLARE @TotalTables INT = 0;
DECLARE @SuccessTables INT = 0;
DECLARE @FailedTables INT = 0;

-- 获取所有以dc开头的表
DECLARE table_cursor CURSOR FOR
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME LIKE 'dc%' 
  AND TABLE_TYPE = 'BASE TABLE'
  AND TABLE_SCHEMA = 'dbo'
  AND EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = INFORMATION_SCHEMA.TABLES.TABLE_NAME AND TABLE_SCHEMA = 'dbo')
ORDER BY TABLE_NAME;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    SET @TotalTables = @TotalTables + 1;
    
    PRINT '========================================';
    PRINT '正在迁移 ' + @TableName + ' 表... (' + CAST(@TotalTables AS NVARCHAR(10)) + ')';
    PRINT '========================================';
    
    BEGIN TRY
        -- 重置变量
        SET @InsertColumns = '';
        SET @SelectColumns = '';
        
        -- 生成INSERT字段列表
        SELECT @InsertColumns = @InsertColumns + 
            CASE WHEN @InsertColumns = '' THEN '' ELSE ', ' END + 
            COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = @TableName 
          AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
          AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;
        
        -- 生成SELECT字段列表
        SELECT @SelectColumns = @SelectColumns + 
            CASE WHEN @SelectColumns = '' THEN '' ELSE ', ' END + 
            CASE 
                -- SchoolId 和 UnitId 字段统一使用固定单位ID
                WHEN COLUMN_NAME IN ('SchoolId', 'UnitId', 'OrganizationId', 'DepartmentId') 
                THEN CAST(@unitId AS NVARCHAR(20))
                
                -- 用户ID相关字段使用固定用户ID
                WHEN COLUMN_NAME LIKE '%UserId' OR COLUMN_NAME IN ('CreateUserId', 'UpdateUserId', 'CreatedBy', 'UpdatedBy', 'CreateId', 'ModifyId', 'CreateBy', 'ModifyBy')
                THEN CAST(@userid AS NVARCHAR(20))
                
                -- 时间字段如果是Create/Modify相关，使用GETDATE()
                WHEN COLUMN_NAME IN ('CreateTime', 'ModifyTime', 'UpdateTime', 'RegDate') 
                THEN 'GETDATE()'
                
                -- 删除标记字段默认为0
                WHEN COLUMN_NAME IN ('IsDeleted', 'Deleted', 'IsDelete') 
                THEN '0'
                
                -- 版本字段默认为0
                WHEN COLUMN_NAME IN ('Version', 'VersionNo') 
                THEN '0'
                
                -- 状态字段默认为1
                WHEN COLUMN_NAME IN ('Status', 'Statuz', 'State') 
                THEN '1'
                
                -- 其他字段直接使用源表字段
                ELSE 'ISNULL(src.' + COLUMN_NAME + ', ' +
                    CASE 
                        WHEN DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint', 'bit') THEN '0'
                        WHEN DATA_TYPE IN ('decimal', 'numeric', 'float', 'real', 'money', 'smallmoney') THEN '0.0'
                        WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN ''''''
                        WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime', 'date', 'time') THEN 'GETDATE()'
                        WHEN DATA_TYPE = 'uniqueidentifier' THEN 'NEWID()'
                        ELSE '0'
                    END + ')'
            END
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = @TableName 
          AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
          AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;
        
        -- 构建并执行INSERT语句
        SET @SQL = 'INSERT INTO dbo.' + @TableName + ' (' + @InsertColumns + ') ' +
                   'SELECT ' + @SelectColumns + ' ' +
                   'FROM Hyun.dbo.' + @TableName + ' src ' +
                   'WHERE NOT EXISTS (SELECT 1 FROM ' + @TableName + ' target WHERE target.Id = src.Id)';
        
        -- 显示即将执行的SQL（调试用）
        PRINT '执行SQL: ';
        PRINT SUBSTRING(@SQL, 1, 500) + CASE WHEN LEN(@SQL) > 500 THEN '...' ELSE '' END;
        PRINT '';
        
        -- 执行SQL
        EXEC sp_executesql @SQL;
        
        SET @SuccessTables = @SuccessTables + 1;
        PRINT @TableName + ' 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
        
    END TRY
    BEGIN CATCH
        SET @FailedTables = @FailedTables + 1;
        PRINT @TableName + ' 表迁移失败: ' + ERROR_MESSAGE();
        PRINT '建议：手动调整该表的字段映射';
        
        -- 输出建议的手动脚本
        PRINT '';
        PRINT '-- 建议的手动脚本模板：';
        PRINT 'INSERT INTO dbo.' + @TableName + ' (' + ISNULL(@InsertColumns, '字段列表') + ')';
        PRINT 'SELECT ' + ISNULL(@SelectColumns, '对应字段或默认值') + ' FROM Hyun.dbo.' + @TableName + ' src';
        PRINT 'WHERE NOT EXISTS (SELECT 1 FROM ' + @TableName + ' target WHERE target.Id = src.Id);';
        PRINT '';
    END CATCH
    
    PRINT '';
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '========================================';
PRINT '迁移汇总';
PRINT '========================================';
PRINT '总表数: ' + CAST(@TotalTables AS NVARCHAR(10));
PRINT '成功: ' + CAST(@SuccessTables AS NVARCHAR(10));
PRINT '失败: ' + CAST(@FailedTables AS NVARCHAR(10));
PRINT '完成时间: ' + CONVERT(NVARCHAR(20), GETDATE(), 120);

IF @FailedTables > 0
BEGIN
    PRINT '';
    PRINT '注意：有 ' + CAST(@FailedTables AS NVARCHAR(10)) + ' 个表迁移失败';
    PRINT '请查看上面的错误信息，手动调整字段映射';
    PRINT '常用默认值：';
    PRINT '  数值: 0';
    PRINT '  字符串: ''''';
    PRINT '  日期: GETDATE()';
    PRINT '  GUID: NEWID()';
    PRINT '  用户ID: ' + CAST(@userid AS NVARCHAR(20));
    PRINT '  单位ID: ' + CAST(@unitId AS NVARCHAR(20));
END

-- 显示迁移后各表的记录数
PRINT '';
PRINT '各表记录数统计：';
DECLARE stats_cursor CURSOR FOR
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME LIKE 'dc%' 
  AND TABLE_TYPE = 'BASE TABLE'
  AND TABLE_SCHEMA = 'dbo'
ORDER BY TABLE_NAME;

OPEN stats_cursor;
FETCH NEXT FROM stats_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    SET @SQL = 'SELECT ''' + @TableName + ' 记录数: '' + CAST(COUNT(*) AS NVARCHAR(10)) FROM ' + @TableName;
    EXEC sp_executesql @SQL;
    
    FETCH NEXT FROM stats_cursor INTO @TableName;
END

CLOSE stats_cursor;
DEALLOCATE stats_cursor;

GO
