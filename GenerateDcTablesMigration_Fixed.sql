-- 修复版：生成所有dc开头表的迁移脚本
-- 确保逗号格式正确

USE HyunCoreBase;
GO

DECLARE @userid BIGINT = 592123693924485;
DECLARE @unitId BIGINT = 592123124052101;

PRINT '========================================';
PRINT '生成dc开头表的迁移脚本 (修复版)';
PRINT '========================================';
PRINT '用户ID: ' + CAST(@userid AS NVARCHAR(20));
PRINT '单位ID: ' + CAST(@unitId AS NVARCHAR(20));
PRINT '';

-- 获取所有以dc开头的表
DECLARE @TableName NVARCHAR(128);

DECLARE table_cursor CURSOR FOR
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME LIKE 'dc%' 
  AND TABLE_TYPE = 'BASE TABLE'
  AND TABLE_SCHEMA = 'dbo'
ORDER BY TABLE_NAME;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '========================================';
    PRINT '-- ' + @TableName + ' 表迁移脚本';
    PRINT '========================================';
    
    -- 检查源表是否存在
    IF EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo')
    BEGIN
        PRINT 'PRINT ''正在迁移 ' + @TableName + ' 表...'';';
        PRINT '';
        PRINT 'BEGIN TRY';
        PRINT '    INSERT INTO dbo.' + @TableName;
        PRINT '    (';
        
        -- 生成INSERT字段列表，每行一个字段
        DECLARE @IsFirstColumn BIT = 1;
        DECLARE @ColumnName NVARCHAR(128);
        
        DECLARE column_cursor CURSOR FOR
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = @TableName 
          AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
          AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;
        
        OPEN column_cursor;
        FETCH NEXT FROM column_cursor INTO @ColumnName;
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            IF @IsFirstColumn = 1
            BEGIN
                PRINT '        ' + @ColumnName;
                SET @IsFirstColumn = 0;
            END
            ELSE
            BEGIN
                PRINT '        ,' + @ColumnName;
            END
            
            FETCH NEXT FROM column_cursor INTO @ColumnName;
        END
        
        CLOSE column_cursor;
        DEALLOCATE column_cursor;
        
        PRINT '    )';
        PRINT '    SELECT';
        
        -- 生成SELECT字段列表，每行一个字段
        SET @IsFirstColumn = 1;
        
        DECLARE column_cursor2 CURSOR FOR
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = @TableName 
          AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
          AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;
        
        DECLARE @DataType NVARCHAR(128);
        OPEN column_cursor2;
        FETCH NEXT FROM column_cursor2 INTO @ColumnName, @DataType;
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            DECLARE @SelectValue NVARCHAR(500);
            
            -- 检查源表是否有此字段
            DECLARE @SourceHasColumn BIT = 0;
            IF EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.COLUMNS
                      WHERE TABLE_NAME = @TableName AND COLUMN_NAME = @ColumnName AND TABLE_SCHEMA = 'dbo')
                SET @SourceHasColumn = 1;

            -- 根据字段名和类型确定SELECT值
            SET @SelectValue =
                CASE
                    -- 如果源表中有此字段，优先使用源表的值
                    WHEN @SourceHasColumn = 1 AND @ColumnName IN ('SchoolId', 'UnitId', 'OrganizationId', 'DepartmentId')
                    THEN CAST(@unitId AS NVARCHAR(20)) + ' AS ' + @ColumnName + ' -- 固定单位ID（覆盖源表值）'

                    WHEN @SourceHasColumn = 1 AND (@ColumnName LIKE '%UserId' OR @ColumnName IN ('CreateUserId', 'UpdateUserId', 'CreatedBy', 'UpdatedBy', 'CreateId', 'ModifyId', 'CreateBy', 'ModifyBy'))
                    THEN CAST(@userid AS NVARCHAR(20)) + ' AS ' + @ColumnName + ' -- 固定用户ID（覆盖源表值）'

                    -- 源表有此字段，直接使用源表值
                    WHEN @SourceHasColumn = 1
                    THEN 'src.' + @ColumnName

                    -- 源表没有此字段，使用默认值
                    WHEN @SourceHasColumn = 0 AND @ColumnName IN ('SchoolId', 'UnitId', 'OrganizationId', 'DepartmentId')
                    THEN CAST(@unitId AS NVARCHAR(20)) + ' AS ' + @ColumnName + ' -- 固定单位ID（默认值）'

                    WHEN @SourceHasColumn = 0 AND (@ColumnName LIKE '%UserId' OR @ColumnName IN ('CreateUserId', 'UpdateUserId', 'CreatedBy', 'UpdatedBy', 'CreateId', 'ModifyId', 'CreateBy', 'ModifyBy'))
                    THEN CAST(@userid AS NVARCHAR(20)) + ' AS ' + @ColumnName + ' -- 固定用户ID（默认值）'

                    WHEN @SourceHasColumn = 0 AND @ColumnName IN ('CreateTime', 'ModifyTime', 'UpdateTime', 'RegDate')
                    THEN 'GETDATE() AS ' + @ColumnName + ' -- 当前时间（默认值）'

                    WHEN @SourceHasColumn = 0 AND @ColumnName IN ('IsDeleted', 'Deleted', 'IsDelete')
                    THEN '0 AS ' + @ColumnName + ' -- 未删除（默认值）'

                    WHEN @SourceHasColumn = 0 AND @ColumnName IN ('Version', 'VersionNo')
                    THEN '0 AS ' + @ColumnName + ' -- 版本号（默认值）'

                    WHEN @SourceHasColumn = 0 AND @ColumnName IN ('Status', 'Statuz', 'State')
                    THEN '1 AS ' + @ColumnName + ' -- 状态（默认值）'

                    -- 源表没有此字段，根据数据类型使用默认值
                    WHEN @SourceHasColumn = 0 AND @DataType IN ('int', 'bigint', 'smallint', 'tinyint', 'bit')
                    THEN '0 AS ' + @ColumnName + ' -- 数值默认值'

                    WHEN @SourceHasColumn = 0 AND @DataType IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext')
                    THEN ''''' AS ' + @ColumnName + ' -- 字符串默认值'

                    WHEN @SourceHasColumn = 0 AND @DataType IN ('datetime', 'datetime2', 'smalldatetime', 'date', 'time')
                    THEN 'GETDATE() AS ' + @ColumnName + ' -- 日期默认值'

                    WHEN @SourceHasColumn = 0 AND @DataType = 'uniqueidentifier'
                    THEN 'NEWID() AS ' + @ColumnName + ' -- GUID默认值'

                    -- 其他情况使用NULL或0
                    ELSE '0 AS ' + @ColumnName + ' -- 默认值'
                END;
            
            IF @IsFirstColumn = 1
            BEGIN
                PRINT '        ' + @SelectValue;
                SET @IsFirstColumn = 0;
            END
            ELSE
            BEGIN
                PRINT '        ,' + @SelectValue;
            END
            
            FETCH NEXT FROM column_cursor2 INTO @ColumnName, @DataType;
        END
        
        CLOSE column_cursor2;
        DEALLOCATE column_cursor2;
        
        PRINT '    FROM Hyun.dbo.' + @TableName + ' src';
        PRINT '    WHERE NOT EXISTS (';
        PRINT '        SELECT 1 FROM ' + @TableName + ' target';
        PRINT '        WHERE target.Id = src.Id';
        PRINT '    );';
        PRINT '';
        PRINT '    PRINT ''' + @TableName + ' 表迁移完成，新增行数: '' + CAST(@@ROWCOUNT AS NVARCHAR(10));';
        PRINT 'END TRY';
        PRINT 'BEGIN CATCH';
        PRINT '    PRINT ''' + @TableName + ' 表迁移失败: '' + ERROR_MESSAGE();';
        PRINT '    PRINT ''请根据错误信息调整字段映射，把报错字段改为默认值'';';
        PRINT 'END CATCH';
        PRINT '';
        PRINT 'PRINT '''';';
        PRINT '';
    END
    ELSE
    BEGIN
        PRINT '-- 源表 Hyun.dbo.' + @TableName + ' 不存在，跳过';
        PRINT '';
    END
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '========================================';
PRINT '-- 脚本生成完成！';
PRINT '========================================';
PRINT '';
PRINT '/*';
PRINT '使用说明：';
PRINT '1. 复制上面生成的INSERT语句到新查询窗口';
PRINT '2. 执行脚本，如果报错"列名无效"或"字段不存在"';
PRINT '3. 就把报错的字段替换成对应的默认值：';
PRINT '   - 数值类型: 0';
PRINT '   - 字符串类型: ''''';
PRINT '   - 日期类型: GETDATE()';
PRINT '   - 布尔类型: 0 或 1';
PRINT '   - GUID类型: NEWID()';
PRINT '';
PRINT '常用默认值示例：';
PRINT '   0 AS SomeIntField           -- 数值字段';
PRINT '   ,''''AS SomeStringField       -- 字符串字段';
PRINT '   ,GETDATE() AS SomeDateField  -- 日期字段';
PRINT '   ,NEWID() AS SomeGuidField    -- GUID字段';
PRINT '   ,0 AS IsDeleted              -- 删除标记';
PRINT '   ,1 AS Status                 -- 状态字段';
PRINT '';
PRINT '固定值：';
PRINT '   SchoolId/UnitId: ' + CAST(@unitId AS NVARCHAR(20));
PRINT '   UserId相关: ' + CAST(@userid AS NVARCHAR(20));
PRINT '*/';

GO
