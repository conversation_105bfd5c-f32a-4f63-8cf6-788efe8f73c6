-- 测试逗号修复的脚本
-- 生成一个表的INSERT语句来验证逗号是否正确

USE HyunCoreBase;
GO

DECLARE @userid BIGINT = 592123693924485;
DECLARE @unitId BIGINT = 592123124052101;
DECLARE @TableName NVARCHAR(128) = 'dc_Apply'; -- 测试表
DECLARE @InsertColumns NVARCHAR(MAX);
DECLARE @SelectColumns NVARCHAR(MAX);

PRINT '测试表: ' + @TableName;
PRINT '========================================';

-- 检查表是否存在
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo')
   AND EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo')
BEGIN
    -- 生成INSERT字段列表
    SET @InsertColumns = '';
    SELECT @InsertColumns = @InsertColumns + 
        CASE WHEN @InsertColumns = '' THEN '' ELSE ',' + CHAR(13) + '          ' END + 
        COLUMN_NAME
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = @TableName 
      AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
      AND TABLE_SCHEMA = 'dbo'
    ORDER BY ORDINAL_POSITION;
    
    -- 生成SELECT字段列表
    SET @SelectColumns = '';
    SELECT @SelectColumns = @SelectColumns + 
        CASE WHEN @SelectColumns = '' THEN '' ELSE ',' + CHAR(13) + '          ' END + 
        CASE 
            -- SchoolId 和 UnitId 字段统一使用固定单位ID
            WHEN COLUMN_NAME IN ('SchoolId', 'UnitId', 'OrganizationId', 'DepartmentId') 
            THEN CAST(@unitId AS NVARCHAR(20)) + ' AS ' + COLUMN_NAME + ' -- 固定单位ID'
            
            -- 用户ID相关字段使用固定用户ID
            WHEN COLUMN_NAME LIKE '%UserId' OR COLUMN_NAME IN ('CreateUserId', 'UpdateUserId', 'CreatedBy', 'UpdatedBy', 'CreateId', 'ModifyId', 'CreateBy', 'ModifyBy')
            THEN CAST(@userid AS NVARCHAR(20)) + ' AS ' + COLUMN_NAME + ' -- 固定用户ID'
            
            -- 时间字段如果是Create/Modify相关，使用GETDATE()
            WHEN COLUMN_NAME IN ('CreateTime', 'ModifyTime', 'UpdateTime', 'RegDate') 
            THEN 'GETDATE() AS ' + COLUMN_NAME + ' -- 当前时间'
            
            -- 删除标记字段默认为0
            WHEN COLUMN_NAME IN ('IsDeleted', 'Deleted', 'IsDelete') 
            THEN '0 AS ' + COLUMN_NAME + ' -- 未删除'
            
            -- 版本字段默认为0
            WHEN COLUMN_NAME IN ('Version', 'VersionNo') 
            THEN '0 AS ' + COLUMN_NAME + ' -- 版本号'
            
            -- 状态字段默认为1
            WHEN COLUMN_NAME IN ('Status', 'Statuz', 'State') 
            THEN '1 AS ' + COLUMN_NAME + ' -- 状态'
            
            -- 其他字段直接使用源表字段（如果报错再改默认值）
            ELSE 'src.' + COLUMN_NAME
        END
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = @TableName 
      AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
      AND TABLE_SCHEMA = 'dbo'
    ORDER BY ORDINAL_POSITION;
    
    -- 输出生成的INSERT语句
    PRINT 'INSERT INTO dbo.' + @TableName;
    PRINT '(';
    PRINT '          ' + @InsertColumns;
    PRINT ')';
    PRINT 'SELECT';
    PRINT '          ' + @SelectColumns;
    PRINT 'FROM Hyun.dbo.' + @TableName + ' src';
    PRINT 'WHERE NOT EXISTS (';
    PRINT '    SELECT 1 FROM ' + @TableName + ' target';
    PRINT '    WHERE target.Id = src.Id';
    PRINT ');';
    
    PRINT '';
    PRINT '检查逗号：';
    PRINT '- INSERT字段列表长度: ' + CAST(LEN(@InsertColumns) AS NVARCHAR(10));
    PRINT '- SELECT字段列表长度: ' + CAST(LEN(@SelectColumns) AS NVARCHAR(10));
    
    -- 检查是否有连续的逗号或缺失的逗号
    IF CHARINDEX(',,', @InsertColumns) > 0
        PRINT '警告: INSERT字段列表中发现连续逗号';
    
    IF CHARINDEX(',,', @SelectColumns) > 0
        PRINT '警告: SELECT字段列表中发现连续逗号';
        
    -- 统计逗号数量
    DECLARE @InsertCommaCount INT = LEN(@InsertColumns) - LEN(REPLACE(@InsertColumns, ',', ''));
    DECLARE @SelectCommaCount INT = LEN(@SelectColumns) - LEN(REPLACE(@SelectColumns, ',', ''));
    
    PRINT '- INSERT字段逗号数: ' + CAST(@InsertCommaCount AS NVARCHAR(10));
    PRINT '- SELECT字段逗号数: ' + CAST(@SelectCommaCount AS NVARCHAR(10));
    
    IF @InsertCommaCount = @SelectCommaCount
        PRINT '✓ 逗号数量匹配';
    ELSE
        PRINT '✗ 逗号数量不匹配';
END
ELSE
BEGIN
    PRINT '表 ' + @TableName + ' 不存在或源表不存在';
END

GO
