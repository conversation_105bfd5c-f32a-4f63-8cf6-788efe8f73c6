-- 清理版本：生成纯净的MySQL INSERT语句
-- 避免在生成的INSERT语句中包含SQL Server语法

-- ========================================
-- 生成 bn_Attachment 表的INSERT语句
-- ========================================

PRINT '-- =========================================='
PRINT '-- bn_Attachment 表的INSERT语句'
PRINT '-- =========================================='

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'bn_Attachment')
BEGIN
    -- 直接生成INSERT语句，不使用动态SQL中的函数调用
    SELECT 
        'INSERT INTO bn_Attachment (' + 
        STUFF((
            SELECT ', ' + COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'bn_Attachment'
            ORDER BY ORDINAL_POSITION
            FOR XML PATH('')
        ), 1, 2, '') + 
        ') VALUES (' +
        STUFF((
            SELECT ', ' + 
                CASE 
                    WHEN c.DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                        CASE WHEN t.[Id] IS NULL THEN 'NULL' -- 这里用Id作为示例，实际需要根据列名调整
                             ELSE '''' + REPLACE(CAST(t.[Id] AS NVARCHAR(MAX)), '''', '''''') + ''''  -- 这里需要替换为实际列名
                        END
                    WHEN c.DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
                        CASE WHEN t.[Id] IS NULL THEN 'NULL' -- 这里需要替换为实际列名
                             ELSE '''' + CONVERT(VARCHAR(19), t.[Id], 120) + ''''  -- 这里需要替换为实际列名
                        END
                    WHEN c.DATA_TYPE = 'date' THEN
                        CASE WHEN t.[Id] IS NULL THEN 'NULL' -- 这里需要替换为实际列名
                             ELSE '''' + CONVERT(VARCHAR(10), t.[Id], 120) + ''''  -- 这里需要替换为实际列名
                        END
                    WHEN c.DATA_TYPE = 'bit' THEN
                        CASE WHEN t.[Id] IS NULL THEN 'NULL' -- 这里需要替换为实际列名
                             ELSE CAST(t.[Id] AS VARCHAR(1))  -- 这里需要替换为实际列名
                        END
                    ELSE 
                        CASE WHEN t.[Id] IS NULL THEN 'NULL' -- 这里需要替换为实际列名
                             ELSE CAST(t.[Id] AS NVARCHAR(MAX))  -- 这里需要替换为实际列名
                        END
                END
            FROM INFORMATION_SCHEMA.COLUMNS c
            WHERE c.TABLE_NAME = 'bn_Attachment'
            ORDER BY c.ORDINAL_POSITION
            FOR XML PATH('')
        ), 1, 2, '') + 
        ') ON DUPLICATE KEY UPDATE Id = Id;' AS InsertStatement
    FROM bn_Attachment t
END
ELSE
BEGIN
    PRINT '-- 表 bn_Attachment 不存在'
END

-- ========================================
-- 简化方案：手动构建每个表的INSERT语句
-- ========================================

PRINT ''
PRINT '-- =========================================='
PRINT '-- 简化方案：请根据实际列名修改以下模板'
PRINT '-- =========================================='

-- 为 bn_Attachment 表生成简单的INSERT语句模板
PRINT '-- bn_Attachment 表模板（请根据实际列名修改）:'
PRINT 'SELECT '
PRINT '    ''INSERT INTO bn_Attachment (Id, BaseIsDelete, BaseCreateTime, BaseModifyTime, BaseCreatorId, BaseModifierId, BaseVersion, ObjectId, FileCategory, Title, Path, Width, Height, Ext, IsDefault, Remark, IsShow, IsDelete, IsAudit, Approver, AuditDate, AuditResult) VALUES ('' +'
PRINT '    CAST(Id AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CAST(BaseIsDelete AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CASE WHEN BaseCreateTime IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), BaseCreateTime, 120) + '''''''''' END + '', '' +'
PRINT '    CASE WHEN BaseModifyTime IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), BaseModifyTime, 120) + '''''''''' END + '', '' +'
PRINT '    CAST(BaseCreatorId AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CAST(BaseModifierId AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CAST(BaseVersion AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CAST(ObjectId AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CAST(FileCategory AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CASE WHEN Title IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(Title, '''''''', '''''''''''') + '''''''''' END + '', '' +'
PRINT '    CASE WHEN Path IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(Path, '''''''', '''''''''''') + '''''''''' END + '', '' +'
PRINT '    CAST(ISNULL(Width, 0) AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CAST(ISNULL(Height, 0) AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CASE WHEN Ext IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(Ext, '''''''', '''''''''''') + '''''''''' END + '', '' +'
PRINT '    CAST(ISNULL(IsDefault, 0) AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CASE WHEN Remark IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(Remark, '''''''', '''''''''''') + '''''''''' END + '', '' +'
PRINT '    CAST(ISNULL(IsShow, 0) AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CAST(ISNULL(IsDelete, 0) AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CAST(ISNULL(IsAudit, 0) AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CAST(ISNULL(Approver, 0) AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CASE WHEN AuditDate IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), AuditDate, 120) + '''''''''' END + '', '' +'
PRINT '    CASE WHEN AuditResult IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(AuditResult, '''''''', '''''''''''') + '''''''''' END +'
PRINT '    '') ON DUPLICATE KEY UPDATE Id = Id;'''
PRINT 'FROM bn_Attachment'
PRINT ''

-- 为 SysUser 表生成模板
PRINT '-- SysUser 表模板（请根据实际列名修改）:'
PRINT 'SELECT '
PRINT '    ''INSERT INTO SysUser (Id, UserName, Password, Email, CreateTime) VALUES ('' +'
PRINT '    CAST(Id AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CASE WHEN UserName IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(UserName, '''''''', '''''''''''') + '''''''''' END + '', '' +'
PRINT '    CASE WHEN Password IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(Password, '''''''', '''''''''''') + '''''''''' END + '', '' +'
PRINT '    CASE WHEN Email IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(Email, '''''''', '''''''''''') + '''''''''' END + '', '' +'
PRINT '    CASE WHEN CreateTime IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), CreateTime, 120) + '''''''''' END +'
PRINT '    '') ON DUPLICATE KEY UPDATE Id = Id;'''
PRINT 'FROM SysUser'
PRINT ''

-- 为 SysUserBelong 表生成模板
PRINT '-- SysUserBelong 表模板（请根据实际列名修改）:'
PRINT 'SELECT '
PRINT '    ''INSERT INTO SysUserBelong (Id, UserId, RoleId, CreateTime) VALUES ('' +'
PRINT '    CAST(Id AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CAST(UserId AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CAST(RoleId AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CASE WHEN CreateTime IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), CreateTime, 120) + '''''''''' END +'
PRINT '    '') ON DUPLICATE KEY UPDATE Id = Id;'''
PRINT 'FROM SysUserBelong'

PRINT ''
PRINT '-- =========================================='
PRINT '-- 使用说明：'
PRINT '-- 1. 复制上面的SELECT语句模板'
PRINT '-- 2. 根据实际的表结构修改列名'
PRINT '-- 3. 执行修改后的SELECT语句'
PRINT '-- 4. 复制查询结果即为最终的INSERT语句'
PRINT '-- 5. 生成的INSERT语句可直接在MySQL中执行'
PRINT '-- =========================================='
