-- 检查表结构并生成正确的INSERT脚本
-- 第一步：检查实际的表结构，然后生成正确的INSERT语句

USE HyunCoreBase;
GO

DECLARE @FixedUserId NVARCHAR(50) = '592123693924485';
DECLARE @FixedUnitId NVARCHAR(50) = '592123124052101';

PRINT '========================================';
PRINT '表结构检查和脚本生成工具';
PRINT '========================================';
PRINT '固定用户ID: ' + @FixedUserId;
PRINT '固定单位ID: ' + @FixedUnitId;
PRINT '';

-- 检查要迁移的表
DECLARE @TableName NVARCHAR(128);
DECLARE table_cursor CURSOR FOR
SELECT 'dc_PurchaseList' AS TableName
UNION ALL
SELECT 'dc_PurchaseOrder'
UNION ALL
SELECT 'dc_PurchaseApproval';

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '========================================';
    PRINT '检查表: ' + @TableName;
    PRINT '========================================';
    
    -- 检查源表是否存在
    IF EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo')
    BEGIN
        PRINT '✓ 源表 Hyun.dbo.' + @TableName + ' 存在';
        PRINT '';
        PRINT '源表字段列表:';
        PRINT '序号 | 字段名 | 数据类型 | 允许空值';
        PRINT '-----|--------|----------|----------';
        
        DECLARE @SourceFields TABLE (
            序号 INT,
            字段名 NVARCHAR(128),
            数据类型 NVARCHAR(128),
            允许空值 NVARCHAR(10)
        );
        
        INSERT INTO @SourceFields
        SELECT 
            ROW_NUMBER() OVER (ORDER BY ORDINAL_POSITION) AS 序号,
            COLUMN_NAME AS 字段名,
            DATA_TYPE + 
            CASE 
                WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL 
                THEN '(' + CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10)) + ')'
                WHEN NUMERIC_PRECISION IS NOT NULL 
                THEN '(' + CAST(NUMERIC_PRECISION AS VARCHAR(10)) + 
                     CASE WHEN NUMERIC_SCALE > 0 THEN ',' + CAST(NUMERIC_SCALE AS VARCHAR(10)) ELSE '' END + ')'
                ELSE ''
            END AS 数据类型,
            CASE WHEN IS_NULLABLE = 'YES' THEN '是' ELSE '否' END AS 允许空值
        FROM Hyun.INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;
        
        SELECT 
            CAST(序号 AS VARCHAR(5)) + ' | ' + 
            字段名 + ' | ' + 
            数据类型 + ' | ' + 
            允许空值 AS 字段信息
        FROM @SourceFields;
        
        DELETE FROM @SourceFields;
    END
    ELSE
    BEGIN
        PRINT '✗ 源表 Hyun.dbo.' + @TableName + ' 不存在';
    END
    
    PRINT '';
    
    -- 检查目标表是否存在
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo')
    BEGIN
        PRINT '✓ 目标表 HyunCoreBase.dbo.' + @TableName + ' 存在';
        PRINT '';
        PRINT '目标表字段列表:';
        PRINT '序号 | 字段名 | 数据类型 | 允许空值 | 自增';
        PRINT '-----|--------|----------|----------|------';
        
        DECLARE @TargetFields TABLE (
            序号 INT,
            字段名 NVARCHAR(128),
            数据类型 NVARCHAR(128),
            允许空值 NVARCHAR(10),
            自增 NVARCHAR(10)
        );
        
        INSERT INTO @TargetFields
        SELECT 
            ROW_NUMBER() OVER (ORDER BY ORDINAL_POSITION) AS 序号,
            COLUMN_NAME AS 字段名,
            DATA_TYPE + 
            CASE 
                WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL 
                THEN '(' + CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10)) + ')'
                WHEN NUMERIC_PRECISION IS NOT NULL 
                THEN '(' + CAST(NUMERIC_PRECISION AS VARCHAR(10)) + 
                     CASE WHEN NUMERIC_SCALE > 0 THEN ',' + CAST(NUMERIC_SCALE AS VARCHAR(10)) ELSE '' END + ')'
                ELSE ''
            END AS 数据类型,
            CASE WHEN IS_NULLABLE = 'YES' THEN '是' ELSE '否' END AS 允许空值,
            CASE 
                WHEN COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 1 
                THEN '是' ELSE '否' 
            END AS 自增
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;
        
        SELECT 
            CAST(序号 AS VARCHAR(5)) + ' | ' + 
            字段名 + ' | ' + 
            数据类型 + ' | ' + 
            允许空值 + ' | ' + 
            自增 AS 字段信息
        FROM @TargetFields;
        
        DELETE FROM @TargetFields;
    END
    ELSE
    BEGIN
        PRINT '✗ 目标表 HyunCoreBase.dbo.' + @TableName + ' 不存在';
    END
    
    PRINT '';
    
    -- 如果两个表都存在，生成INSERT脚本模板
    IF EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo')
       AND EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = 'dbo')
    BEGIN
        PRINT '生成INSERT脚本模板:';
        PRINT '----------------------------------------';
        
        -- 生成INSERT语句模板
        DECLARE @InsertTemplate NVARCHAR(MAX) = '';
        DECLARE @ColumnList NVARCHAR(MAX) = '';
        DECLARE @ValuesList NVARCHAR(MAX) = '';
        
        -- 获取目标表的非自增列
        SELECT 
            @ColumnList = @ColumnList + 
                CASE WHEN @ColumnList = '' THEN '' ELSE ',' + CHAR(13) + '        ' END + 
                COLUMN_NAME,
            @ValuesList = @ValuesList + 
                CASE WHEN @ValuesList = '' THEN '' ELSE ',' + CHAR(13) + '        ' END + 
                CASE 
                    -- 用户ID字段
                    WHEN COLUMN_NAME LIKE '%UserId' OR COLUMN_NAME IN ('CreateUserId', 'UpdateUserId', 'CreatedBy', 'UpdatedBy') 
                    THEN '''' + @FixedUserId + ''' AS ' + COLUMN_NAME + ' -- 固定用户ID'
                    
                    -- 单位ID字段
                    WHEN COLUMN_NAME LIKE '%UnitId' OR COLUMN_NAME IN ('OrganizationId', 'DepartmentId')
                    THEN '''' + @FixedUnitId + ''' AS ' + COLUMN_NAME + ' -- 固定单位ID'
                    
                    -- 检查源表是否有此字段
                    WHEN EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.COLUMNS 
                                WHERE TABLE_NAME = @TableName AND COLUMN_NAME = c.COLUMN_NAME AND TABLE_SCHEMA = 'dbo')
                    THEN 'src.' + COLUMN_NAME + ' -- 从源表获取'
                    
                    -- 源表没有此字段，使用默认值
                    ELSE 
                        CASE 
                            WHEN DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint', 'bit') THEN '0 AS ' + COLUMN_NAME + ' -- 默认值'
                            WHEN DATA_TYPE IN ('decimal', 'numeric', 'float', 'real', 'money', 'smallmoney') THEN '0.0 AS ' + COLUMN_NAME + ' -- 默认值'
                            WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN ''''' AS ' + COLUMN_NAME + ' -- 默认值'
                            WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime', 'date', 'time') THEN 'GETDATE() AS ' + COLUMN_NAME + ' -- 默认值'
                            WHEN DATA_TYPE = 'uniqueidentifier' THEN 'NEWID() AS ' + COLUMN_NAME + ' -- 默认值'
                            WHEN IS_NULLABLE = 'YES' THEN 'NULL AS ' + COLUMN_NAME + ' -- 默认值'
                            ELSE ''''' AS ' + COLUMN_NAME + ' -- 默认值'
                        END
                END
        FROM INFORMATION_SCHEMA.COLUMNS c
        WHERE TABLE_NAME = @TableName 
          AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
          AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;
        
        SET @InsertTemplate = 
            'INSERT INTO ' + @TableName + ' (' + CHAR(13) +
            '        ' + @ColumnList + CHAR(13) +
            ')' + CHAR(13) +
            'SELECT ' + CHAR(13) +
            '        ' + @ValuesList + CHAR(13) +
            'FROM Hyun.dbo.' + @TableName + ' src' + CHAR(13) +
            'WHERE NOT EXISTS (' + CHAR(13) +
            '    SELECT 1 FROM ' + @TableName + ' target ' + CHAR(13) +
            '    WHERE target.Id = src.Id' + CHAR(13) +
            ');';
        
        PRINT @InsertTemplate;
        PRINT '';
    END
    
    PRINT '';
    PRINT '';
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '========================================';
PRINT '检查完成！';
PRINT '========================================';
PRINT '';
PRINT '使用说明：';
PRINT '1. 查看上面的表结构对比';
PRINT '2. 复制生成的INSERT脚本模板';
PRINT '3. 根据实际字段名称调整脚本';
PRINT '4. 在新查询窗口中执行调整后的脚本';
GO
