-- 简单直接的数据迁移方法
-- 按照用户的思路：先写INSERT，再写SELECT，报错就改默认值

USE HyunCoreBase;
GO

DECLARE @userid BIGINT = 592123693924485;
DECLARE @unitId BIGINT = 592123124052101;

PRINT '开始数据迁移...';
PRINT '用户ID: ' + CAST(@userid AS NVARCHAR(20));
PRINT '单位ID: ' + CAST(@unitId AS NVARCHAR(20));
PRINT '';

-- ========================================
-- 1. 迁移 dc_PurchaseList 表
-- ========================================
PRINT '正在迁移 dc_PurchaseList 表...';

BEGIN TRY
    INSERT INTO dbo.dc_PurchaseList
    ( 
        Id,
        PurchaseOrderId,
        BatchNo,
        SchoolId,
        SchoolCatalogId,
        BaseCatalogId,
        SchoolMaterialModelId,
        SchoolMaterialBrandId,
        Name,
        Brand,
        Model,
        Num,
        UnitName,
        Price,
        Remark,
        Statuz,
        RegDate,
        UserId,
        IsDeleted,
        CreateId,
        CreateBy,
        CreateTime,
        ModifyId,
        ModifyBy,
        ModifyTime,
        Version
        -- 如果目标表还有其他字段，继续添加...
    )
    SELECT  
        Id,
        PurchaseOrderId,
        BatchNo,
        @unitId,                    -- SchoolId 用单位ID
        SchoolCatalogId,
        BaseCatalogId,
        SchoolMaterialModelId,
        SchoolMaterialBrandId,
        Name,
        Brand,
        Model,
        Num,
        UnitName,
        Price,
        Remark,
        Statuz,
        RegDate,
        @userid,                    -- UserId 用固定用户ID
        0,                          -- IsDeleted 默认值
        0,                          -- CreateId 默认值
        0,                          -- CreateBy 默认值
        GETDATE(),                  -- CreateTime 默认值
        0,                          -- ModifyId 默认值
        0,                          -- ModifyBy 默认值
        GETDATE(),                  -- ModifyTime 默认值
        0                           -- Version 默认值
        -- 如果源表缺少字段，就用默认值替换
    FROM Hyun.dbo.dc_PurchaseList
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseList target 
        WHERE target.Id = Hyun.dbo.dc_PurchaseList.Id
    );
    
    PRINT 'dc_PurchaseList 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseList 表迁移失败: ' + ERROR_MESSAGE();
    PRINT '请根据错误信息调整字段映射';
END CATCH

PRINT '';

-- ========================================
-- 2. 迁移 dc_PurchaseOrder 表
-- ========================================
PRINT '正在迁移 dc_PurchaseOrder 表...';

BEGIN TRY
    -- TODO: 根据实际目标表结构修改字段列表
    INSERT INTO dbo.dc_PurchaseOrder
    ( 
        Id,
        -- 在这里列出目标表的所有字段...
        CreateUserId,
        UnitId,
        CreateTime,
        IsDeleted
        -- 继续添加其他字段...
    )
    SELECT  
        Id,
        -- 对应的源表字段或默认值...
        @userid,        -- CreateUserId
        @unitId,        -- UnitId  
        GETDATE(),      -- CreateTime
        0               -- IsDeleted
        -- 继续添加对应的字段或默认值...
    FROM Hyun.dbo.dc_PurchaseOrder
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseOrder target 
        WHERE target.Id = Hyun.dbo.dc_PurchaseOrder.Id
    );
    
    PRINT 'dc_PurchaseOrder 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseOrder 表迁移失败: ' + ERROR_MESSAGE();
    PRINT '请根据错误信息调整字段映射';
END CATCH

PRINT '';

-- ========================================
-- 3. 迁移 dc_PurchaseApproval 表
-- ========================================
PRINT '正在迁移 dc_PurchaseApproval 表...';

BEGIN TRY
    -- TODO: 根据实际目标表结构修改字段列表
    INSERT INTO dbo.dc_PurchaseApproval
    ( 
        Id,
        -- 在这里列出目标表的所有字段...
        CreateUserId,
        UnitId,
        CreateTime,
        IsDeleted
        -- 继续添加其他字段...
    )
    SELECT  
        Id,
        -- 对应的源表字段或默认值...
        @userid,        -- CreateUserId
        @unitId,        -- UnitId
        GETDATE(),      -- CreateTime
        0               -- IsDeleted
        -- 继续添加对应的字段或默认值...
    FROM Hyun.dbo.dc_PurchaseApproval
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseApproval target 
        WHERE target.Id = Hyun.dbo.dc_PurchaseApproval.Id
    );
    
    PRINT 'dc_PurchaseApproval 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseApproval 表迁移失败: ' + ERROR_MESSAGE();
    PRINT '请根据错误信息调整字段映射';
END CATCH

PRINT '';
PRINT '========================================';
PRINT '迁移完成！';
PRINT '========================================';

-- 显示统计信息
PRINT '';
PRINT '迁移后统计信息:';
PRINT 'dc_PurchaseList 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseList) AS NVARCHAR(10));
PRINT 'dc_PurchaseOrder 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseOrder) AS NVARCHAR(10));
PRINT 'dc_PurchaseApproval 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseApproval) AS NVARCHAR(10));

PRINT '';
PRINT '使用方法：';
PRINT '1. 根据目标表结构修改INSERT字段列表';
PRINT '2. 对应修改SELECT字段列表';
PRINT '3. 执行脚本，如果报错"字段不存在"';
PRINT '4. 就把那个字段替换成默认值：';
PRINT '   - 数值类型: 0';
PRINT '   - 字符串类型: ''''';
PRINT '   - 日期类型: GETDATE()';
PRINT '   - 用户ID: @userid';
PRINT '   - 单位ID: @unitId';

GO
