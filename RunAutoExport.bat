@echo off
echo ========================================
echo Auto Export SQL Server to MySQL Scripts
echo ========================================
echo.
echo This will automatically:
echo 1. Connect to SQL Server (***********/syjx)
echo 2. Find all tables with 'id' field
echo 3. Export data from each table
echo 4. Generate MySQL INSERT scripts
echo 5. Save each table as separate .sql file
echo.
echo Output directory: .\mysql_scripts\
echo.
pause

echo Starting PowerShell script...
powershell -ExecutionPolicy Bypass -File "AutoExportToMySql.ps1"

echo.
echo ========================================
echo Check the mysql_scripts folder for generated files
echo Each file contains INSERT IGNORE statements for MySQL
echo ========================================
pause
