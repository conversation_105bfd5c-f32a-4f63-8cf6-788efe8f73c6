@echo off
echo ========================================
echo 数据迁移工具
echo ========================================
echo.
echo 正在连接到SQL Server...
echo 服务器: 192.168.2.3
echo 用户: hyun
echo.

REM 使用sqlcmd执行SQL脚本
sqlcmd -S 192.168.2.3 -U hyun -P hyun -i FlexibleDataMigration.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo 数据迁移完成！
    echo ========================================
) else (
    echo.
    echo ========================================
    echo 数据迁移失败！错误代码: %ERRORLEVEL%
    echo ========================================
)

echo.
pause
