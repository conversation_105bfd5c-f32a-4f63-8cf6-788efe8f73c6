-- SQL Server到MySQL数据迁移脚本
-- 从SQL Server数据库(syjx)导出数据并导入到MySQL数据库
-- 连接信息：
-- SQL Server: Data Source=192.168.2.3;Initial Catalog=syjx;User ID=hyun;Password=hyun;TrustServerCertificate=true
-- MySQL: server=192.168.2.3;database=syjx;user=root;password=123456;port=3306

USE syjx;
GO

-- 声明变量
DECLARE @TableName NVARCHAR(128);
DECLARE @SQL NVARCHAR(MAX);
DECLARE @ColumnList NVARCHAR(MAX);
DECLARE @SelectList NVARCHAR(MAX);
DECLARE @MySqlInsertSQL NVARCHAR(MAX);
DECLARE @RowCount INT;

PRINT '开始生成SQL Server到MySQL的数据迁移脚本...';
PRINT '======================================================';

-- 获取当前数据库中的所有表
DECLARE table_cursor CURSOR FOR
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '';
    PRINT '-- ======================================================';
    PRINT '-- 处理表: ' + @TableName;
    PRINT '-- ======================================================';
    
    -- 检查表是否有id字段
    IF EXISTS (
        SELECT 1 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = @TableName 
        AND COLUMN_NAME = 'id'
        AND DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint')
    )
    BEGIN
        -- 获取表的所有列
        SELECT @ColumnList = STRING_AGG(COLUMN_NAME, ', ')
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @TableName
        ORDER BY ORDINAL_POSITION;

        -- 生成SELECT语句的列列表（处理特殊字符和NULL值）
        SELECT @SelectList = STRING_AGG(
            CASE 
                WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                    'ISNULL(REPLACE(REPLACE(REPLACE(' + COLUMN_NAME + ', CHAR(13), ''''), CHAR(10), ''''), '''''''', ''''''''''''), '''''''')'
                WHEN DATA_TYPE IN ('datetime', 'datetime2', 'date', 'time') THEN
                    'ISNULL(CONVERT(VARCHAR(23), ' + COLUMN_NAME + ', 121), ''NULL'')'
                WHEN DATA_TYPE IN ('bit') THEN
                    'ISNULL(CAST(' + COLUMN_NAME + ' AS VARCHAR(1)), ''0'')'
                ELSE
                    'ISNULL(CAST(' + COLUMN_NAME + ' AS VARCHAR(50)), ''NULL'')'
            END, ', '
        )
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @TableName
        ORDER BY ORDINAL_POSITION;

        -- 生成查询数据的SQL
        SET @SQL = '
-- 查询表 ' + @TableName + ' 的数据
SELECT ' + @SelectList + '
FROM ' + @TableName + '
ORDER BY id;';

        PRINT @SQL;
        PRINT '';

        -- 生成MySQL INSERT语句模板
        PRINT '-- 对应的MySQL INSERT语句模板：';
        PRINT 'INSERT INTO ' + @TableName + ' (' + @ColumnList + ')';
        PRINT 'SELECT * FROM (';
        PRINT '    -- 这里粘贴上面查询的结果，每行数据格式如下：';
        PRINT '    -- SELECT ''value1'', ''value2'', ... UNION ALL';
        PRINT '    -- SELECT ''value1'', ''value2'', ... UNION ALL';
        PRINT '    -- 最后一行不要UNION ALL';
        PRINT ') AS temp_data';
        PRINT 'WHERE NOT EXISTS (';
        PRINT '    SELECT 1 FROM ' + @TableName + ' target';
        PRINT '    WHERE target.id = temp_data.id';
        PRINT ');';
        PRINT '';
        
    END
    ELSE
    BEGIN
        PRINT '-- 表 ' + @TableName + ' 没有id字段，跳过处理';
    END
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '';
PRINT '======================================================';
PRINT '脚本生成完成！';
PRINT '使用说明：';
PRINT '1. 在SQL Server中执行上面生成的SELECT语句';
PRINT '2. 将查询结果复制到MySQL中';
PRINT '3. 使用生成的INSERT模板在MySQL中执行插入';
PRINT '4. MySQL会自动判断id是否存在，不存在才插入';
PRINT '======================================================';
GO
