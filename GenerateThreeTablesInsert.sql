-- 生成bn_Attachment、<PERSON><PERSON><PERSON>ser、<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>三个表的INSERT语句
-- 在SQL Server查询分析器中执行此脚本

DECLARE @sql NVARCHAR(MAX) = ''
DECLARE @tableName NVARCHAR(128)
DECLARE @columnList NVARCHAR(MAX)
DECLARE @valuesList NVARCHAR(MAX)
DECLARE @insertStatement NVARCHAR(MAX)

-- 要处理的表名列表
DECLARE table_cursor CURSOR FOR
SELECT 'bn_Attachment' AS TableName
UNION ALL
SELECT 'SysUser'
UNION ALL
SELECT 'SysUserBelong'

OPEN table_cursor
FETCH NEXT FROM table_cursor INTO @tableName

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '-- =========================================='
    PRINT '-- 表: ' + @tableName
    PRINT '-- =========================================='
    PRINT ''
    
    -- 检查表是否存在
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @tableName)
    BEGIN
        -- 获取列信息
        DECLARE @columns TABLE (
            COLUMN_NAME NVARCHAR(128),
            DATA_TYPE NVARCHAR(128),
            ORDINAL_POSITION INT
        )
        
        INSERT INTO @columns (COLUMN_NAME, DATA_TYPE, ORDINAL_POSITION)
        SELECT COLUMN_NAME, DATA_TYPE, ORDINAL_POSITION
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @tableName
        ORDER BY ORDINAL_POSITION
        
        -- 构建列名列表
        SELECT @columnList = STUFF((
            SELECT ', ' + COLUMN_NAME
            FROM @columns
            ORDER BY ORDINAL_POSITION
            FOR XML PATH('')
        ), 1, 2, '')
        
        -- 生成INSERT语句头部
        SET @insertStatement = 'INSERT INTO ' + @tableName + ' (' + @columnList + ') VALUES'
        PRINT @insertStatement
        
        -- 动态构建SELECT语句来生成VALUES
        DECLARE @selectSql NVARCHAR(MAX) = 'SELECT ''('' + '
        
        DECLARE @colName NVARCHAR(128)
        DECLARE @dataType NVARCHAR(128)
        DECLARE @isFirst BIT = 1
        
        DECLARE col_cursor CURSOR FOR
        SELECT COLUMN_NAME, DATA_TYPE
        FROM @columns
        ORDER BY ORDINAL_POSITION
        
        OPEN col_cursor
        FETCH NEXT FROM col_cursor INTO @colName, @dataType
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            IF @isFirst = 0
                SET @selectSql = @selectSql + ' + '', '' + '
            ELSE
                SET @isFirst = 0
            
            -- 根据数据类型处理值的格式
            IF @dataType IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext')
            BEGIN
                SET @selectSql = @selectSql + 'CASE WHEN [' + @colName + '] IS NULL THEN ''NULL'' ELSE '''''''' + REPLACE(REPLACE(CAST([' + @colName + '] AS NVARCHAR(MAX)), '''''''', ''''''''''), CHAR(13) + CHAR(10), '' '') + '''''''' END'
            END
            ELSE IF @dataType IN ('datetime', 'datetime2', 'smalldatetime')
            BEGIN
                SET @selectSql = @selectSql + 'CASE WHEN [' + @colName + '] IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(19), [' + @colName + '], 120) + '''''''' END'
            END
            ELSE IF @dataType = 'date'
            BEGIN
                SET @selectSql = @selectSql + 'CASE WHEN [' + @colName + '] IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(10), [' + @colName + '], 120) + '''''''' END'
            END
            ELSE IF @dataType = 'bit'
            BEGIN
                SET @selectSql = @selectSql + 'CASE WHEN [' + @colName + '] IS NULL THEN ''NULL'' ELSE CAST([' + @colName + '] AS VARCHAR(1)) END'
            END
            ELSE
            BEGIN
                SET @selectSql = @selectSql + 'CASE WHEN [' + @colName + '] IS NULL THEN ''NULL'' ELSE CAST([' + @colName + '] AS NVARCHAR(MAX)) END'
            END
            
            FETCH NEXT FROM col_cursor INTO @colName, @dataType
        END
        
        CLOSE col_cursor
        DEALLOCATE col_cursor
        
        SET @selectSql = @selectSql + ' + '')'' + CASE WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = (SELECT COUNT(*) FROM [' + @tableName + ']) THEN '''' ELSE '','' END AS InsertValue FROM [' + @tableName + ']'
        
        -- 执行动态SQL生成VALUES部分
        DECLARE @valueTable TABLE (InsertValue NVARCHAR(MAX))
        INSERT INTO @valueTable
        EXEC sp_executesql @selectSql
        
        -- 输出VALUES
        DECLARE @value NVARCHAR(MAX)
        DECLARE value_cursor CURSOR FOR
        SELECT InsertValue FROM @valueTable
        
        OPEN value_cursor
        FETCH NEXT FROM value_cursor INTO @value
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            PRINT @value
            FETCH NEXT FROM value_cursor INTO @value
        END
        
        CLOSE value_cursor
        DEALLOCATE value_cursor
        
        -- 添加ON DUPLICATE KEY UPDATE子句
        PRINT 'ON DUPLICATE KEY UPDATE Id = Id;'
        PRINT ''
        PRINT ''
        
        -- 清理临时表
        DELETE FROM @columns
    END
    ELSE
    BEGIN
        PRINT '-- 表 ' + @tableName + ' 不存在'
        PRINT ''
    END
    
    FETCH NEXT FROM table_cursor INTO @tableName
END

CLOSE table_cursor
DEALLOCATE table_cursor

PRINT '-- 脚本执行完成'
