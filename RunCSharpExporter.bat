@echo off
echo ========================================
echo SQL Server to MySQL C# Exporter
echo ========================================
echo.
echo Building and running C# application...
echo.

dotnet build --configuration Release

if %ERRORLEVEL% EQU 0 (
    echo Build successful! Running application...
    echo.
    dotnet run --configuration Release
) else (
    echo Build failed! Error code: %ERRORLEVEL%
    echo.
    echo Please check:
    echo 1. .NET 6.0 SDK is installed
    echo 2. All required packages are restored
    echo 3. No compilation errors in the code
)

echo.
pause
