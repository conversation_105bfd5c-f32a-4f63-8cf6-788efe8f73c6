-- 最简单的INSERT语句生成器
-- 直接生成可用的INSERT语句，避免复杂的动态SQL

-- ========================================
-- 第一步：查看表结构
-- ========================================

-- 查看 bn_Attachment 表结构
SELECT 'bn_Attachment 表结构:' AS TableInfo
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'bn_Attachment' 
ORDER BY ORDINAL_POSITION

-- 查看 SysUser 表结构  
SELECT 'SysUser 表结构:' AS TableInfo
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'SysUser' 
ORDER BY ORDINAL_POSITION

-- 查看 SysUserBelong 表结构
SELECT 'SysUserBelong 表结构:' AS TableInfo
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'SysUserBelong' 
ORDER BY ORDINAL_POSITION

-- ========================================
-- 第二步：生成INSERT语句（基于您提供的列名）
-- ========================================

-- bn_Attachment 表 - 基于您提供的列名
SELECT 
    'INSERT INTO bn_Attachment (Id, BaseIsDelete, BaseCreateTime, BaseModifyTime, BaseCreatorId, BaseModifierId, BaseVersion, ObjectId, FileCategory, Title, Path, Width, Height, Ext, IsDefault, Remark, IsShow, IsDelete, IsAudit, Approver, AuditDate, AuditResult) VALUES (' +
    CAST(Id AS NVARCHAR(MAX)) + ', ' +
    CAST(BaseIsDelete AS NVARCHAR(MAX)) + ', ' +
    CASE WHEN BaseCreateTime IS NULL THEN 'NULL' ELSE '''' + CONVERT(VARCHAR(19), BaseCreateTime, 120) + '''' END + ', ' +
    CASE WHEN BaseModifyTime IS NULL THEN 'NULL' ELSE '''' + CONVERT(VARCHAR(19), BaseModifyTime, 120) + '''' END + ', ' +
    CAST(BaseCreatorId AS NVARCHAR(MAX)) + ', ' +
    CAST(BaseModifierId AS NVARCHAR(MAX)) + ', ' +
    CAST(BaseVersion AS NVARCHAR(MAX)) + ', ' +
    CAST(ObjectId AS NVARCHAR(MAX)) + ', ' +
    CAST(FileCategory AS NVARCHAR(MAX)) + ', ' +
    CASE WHEN Title IS NULL THEN 'NULL' ELSE '''' + REPLACE(Title, '''', '''''') + '''' END + ', ' +
    CASE WHEN Path IS NULL THEN 'NULL' ELSE '''' + REPLACE(Path, '''', '''''') + '''' END + ', ' +
    CAST(ISNULL(Width, 0) AS NVARCHAR(MAX)) + ', ' +
    CAST(ISNULL(Height, 0) AS NVARCHAR(MAX)) + ', ' +
    CASE WHEN Ext IS NULL THEN 'NULL' ELSE '''' + REPLACE(Ext, '''', '''''') + '''' END + ', ' +
    CAST(ISNULL(IsDefault, 0) AS NVARCHAR(MAX)) + ', ' +
    CASE WHEN Remark IS NULL THEN 'NULL' ELSE '''' + REPLACE(Remark, '''', '''''') + '''' END + ', ' +
    CAST(ISNULL(IsShow, 0) AS NVARCHAR(MAX)) + ', ' +
    CAST(ISNULL(IsDelete, 0) AS NVARCHAR(MAX)) + ', ' +
    CAST(ISNULL(IsAudit, 0) AS NVARCHAR(MAX)) + ', ' +
    CAST(ISNULL(Approver, 0) AS NVARCHAR(MAX)) + ', ' +
    CASE WHEN AuditDate IS NULL THEN 'NULL' ELSE '''' + CONVERT(VARCHAR(19), AuditDate, 120) + '''' END + ', ' +
    CASE WHEN AuditResult IS NULL THEN 'NULL' ELSE '''' + REPLACE(AuditResult, '''', '''''') + '''' END +
    ') ON DUPLICATE KEY UPDATE Id = Id;' AS InsertStatement
FROM bn_Attachment

-- ========================================
-- SysUser 表 - 请根据实际列名修改
-- ========================================

-- 先查看SysUser表的实际列名，然后修改下面的查询
-- 示例（请根据实际列名修改）：
/*
SELECT 
    'INSERT INTO SysUser (Id, UserName, Password, Email, CreateTime, IsActive) VALUES (' +
    CAST(Id AS NVARCHAR(MAX)) + ', ' +
    CASE WHEN UserName IS NULL THEN 'NULL' ELSE '''' + REPLACE(UserName, '''', '''''') + '''' END + ', ' +
    CASE WHEN Password IS NULL THEN 'NULL' ELSE '''' + REPLACE(Password, '''', '''''') + '''' END + ', ' +
    CASE WHEN Email IS NULL THEN 'NULL' ELSE '''' + REPLACE(Email, '''', '''''') + '''' END + ', ' +
    CASE WHEN CreateTime IS NULL THEN 'NULL' ELSE '''' + CONVERT(VARCHAR(19), CreateTime, 120) + '''' END + ', ' +
    CAST(ISNULL(IsActive, 0) AS NVARCHAR(MAX)) +
    ') ON DUPLICATE KEY UPDATE Id = Id;' AS InsertStatement
FROM SysUser
*/

-- ========================================
-- SysUserBelong 表 - 请根据实际列名修改
-- ========================================

-- 先查看SysUserBelong表的实际列名，然后修改下面的查询
-- 示例（请根据实际列名修改）：
/*
SELECT 
    'INSERT INTO SysUserBelong (Id, UserId, RoleId, CreateTime) VALUES (' +
    CAST(Id AS NVARCHAR(MAX)) + ', ' +
    CAST(UserId AS NVARCHAR(MAX)) + ', ' +
    CAST(RoleId AS NVARCHAR(MAX)) + ', ' +
    CASE WHEN CreateTime IS NULL THEN 'NULL' ELSE '''' + CONVERT(VARCHAR(19), CreateTime, 120) + '''' END +
    ') ON DUPLICATE KEY UPDATE Id = Id;' AS InsertStatement
FROM SysUserBelong
*/

-- ========================================
-- 使用说明
-- ========================================

/*
使用步骤：
1. 执行第一步查看表结构
2. 根据实际列名修改第二步中的SELECT语句
3. 执行修改后的SELECT语句
4. 复制查询结果即为最终的INSERT语句
5. 生成的INSERT语句可直接在MySQL中执行

注意事项：
- 日期格式使用 YYYY-MM-DD HH:mm:ss，MySQL兼容
- 字符串自动转义单引号
- NULL值正确处理
- 每条数据生成一个独立的INSERT语句
- 包含 ON DUPLICATE KEY UPDATE Id = Id 处理重复记录
*/
