-- 简单版本：生成bn_Attachment、<PERSON><PERSON><PERSON><PERSON>、<PERSON><PERSON><PERSON>serBelong三个表的INSERT语句
-- 在SQL Server查询分析器中执行

-- ========================================
-- 表: bn_Attachment
-- ========================================

PRINT '-- =========================================='
PRINT '-- 表: bn_Attachment'
PRINT '-- =========================================='

-- 检查表是否存在并生成INSERT语句
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'bn_Attachment')
BEGIN
    -- 获取列名
    DECLARE @cols_att NVARCHAR(MAX)
    SELECT @cols_att = STUFF((
        SELECT ', ' + COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'bn_Attachment'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 2, '')
    
    PRINT 'INSERT INTO bn_Attachment (' + @cols_att + ') VALUES'
    
    -- 生成VALUES部分 - 这里需要根据实际数据类型调整
    DECLARE @sql_att NVARCHAR(MAX) = '
    SELECT 
        CASE 
            WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = 1 THEN ''''
            ELSE '',''
        END +
        ''('' + 
        STUFF((
            SELECT '', '' + 
                CASE 
                    WHEN c.DATA_TYPE IN (''varchar'', ''nvarchar'', ''char'', ''nchar'', ''text'', ''ntext'') 
                        THEN CASE WHEN v.val IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(CAST(v.val AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''''' END
                    WHEN c.DATA_TYPE IN (''datetime'', ''datetime2'', ''smalldatetime'') 
                        THEN CASE WHEN v.val IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), CAST(v.val AS DATETIME), 120) + '''''''''' END
                    WHEN c.DATA_TYPE = ''date'' 
                        THEN CASE WHEN v.val IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(10), CAST(v.val AS DATE), 120) + '''''''''' END
                    WHEN c.DATA_TYPE = ''bit'' 
                        THEN CASE WHEN v.val IS NULL THEN ''NULL'' ELSE CAST(v.val AS VARCHAR(1)) END
                    ELSE CASE WHEN v.val IS NULL THEN ''NULL'' ELSE CAST(v.val AS NVARCHAR(MAX)) END
                END
            FROM (
                SELECT ' + 
                STUFF((
                    SELECT ', [' + COLUMN_NAME + '] AS [' + COLUMN_NAME + ']'
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = 'bn_Attachment'
                    ORDER BY ORDINAL_POSITION
                    FOR XML PATH('')
                ), 1, 2, '') + '
                FROM bn_Attachment t
                WHERE t.Id = main.Id
            ) unpiv
            CROSS APPLY (VALUES ' +
                STUFF((
                    SELECT '), (''' + COLUMN_NAME + ''', [' + COLUMN_NAME + ']'
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = 'bn_Attachment'
                    ORDER BY ORDINAL_POSITION
                    FOR XML PATH('')
                ), 1, 4, '(''') + ')) v(col, val)
            INNER JOIN INFORMATION_SCHEMA.COLUMNS c ON c.COLUMN_NAME = v.col AND c.TABLE_NAME = ''bn_Attachment''
            ORDER BY c.ORDINAL_POSITION
            FOR XML PATH('''')
        ), 1, 2, '''') +
        '')'' +
        CASE 
            WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = (SELECT COUNT(*) FROM bn_Attachment) 
            THEN ''''
            ELSE ''''
        END
    FROM bn_Attachment main'
    
    -- 由于动态SQL复杂，我们使用更简单的方法
    PRINT '-- 请手动复制以下查询结果:'
    PRINT 'SELECT ''('' + '
    
    -- 为每个列生成相应的转换逻辑
    DECLARE @col_name NVARCHAR(128)
    DECLARE @data_type NVARCHAR(128)
    DECLARE @col_sql NVARCHAR(MAX) = ''
    DECLARE @first BIT = 1
    
    DECLARE col_cursor CURSOR FOR
    SELECT COLUMN_NAME, DATA_TYPE
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'bn_Attachment'
    ORDER BY ORDINAL_POSITION
    
    OPEN col_cursor
    FETCH NEXT FROM col_cursor INTO @col_name, @data_type
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        IF @first = 0
            SET @col_sql = @col_sql + ' + '', '' + '
        ELSE
            SET @first = 0
        
        IF @data_type IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext')
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(CAST([' + @col_name + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''''' END'
        ELSE IF @data_type IN ('datetime', 'datetime2', 'smalldatetime')
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), [' + @col_name + '], 120) + '''''''''' END'
        ELSE IF @data_type = 'date'
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(10), [' + @col_name + '], 120) + '''''''''' END'
        ELSE IF @data_type = 'bit'
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE CAST([' + @col_name + '] AS VARCHAR(1)) END'
        ELSE
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE CAST([' + @col_name + '] AS NVARCHAR(MAX)) END'
        
        FETCH NEXT FROM col_cursor INTO @col_name, @data_type
    END
    
    CLOSE col_cursor
    DEALLOCATE col_cursor
    
    SET @col_sql = @col_sql + ' + '')'' + CASE WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = (SELECT COUNT(*) FROM bn_Attachment) THEN '''' ELSE '','' END FROM bn_Attachment'
    
    PRINT @col_sql
    PRINT 'ON DUPLICATE KEY UPDATE Id = Id;'
    PRINT ''
END
ELSE
BEGIN
    PRINT '-- 表 bn_Attachment 不存在'
END

-- ========================================
-- 表: SysUser
-- ========================================

PRINT '-- =========================================='
PRINT '-- 表: SysUser'
PRINT '-- =========================================='

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SysUser')
BEGIN
    DECLARE @cols_user NVARCHAR(MAX)
    SELECT @cols_user = STUFF((
        SELECT ', ' + COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'SysUser'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 2, '')
    
    PRINT 'INSERT INTO SysUser (' + @cols_user + ') VALUES'
    
    PRINT '-- 请手动复制以下查询结果:'
    PRINT 'SELECT ''('' + '
    
    SET @col_sql = ''
    SET @first = 1
    
    DECLARE col_cursor2 CURSOR FOR
    SELECT COLUMN_NAME, DATA_TYPE
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'SysUser'
    ORDER BY ORDINAL_POSITION
    
    OPEN col_cursor2
    FETCH NEXT FROM col_cursor2 INTO @col_name, @data_type
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        IF @first = 0
            SET @col_sql = @col_sql + ' + '', '' + '
        ELSE
            SET @first = 0
        
        IF @data_type IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext')
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(CAST([' + @col_name + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''''' END'
        ELSE IF @data_type IN ('datetime', 'datetime2', 'smalldatetime')
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), [' + @col_name + '], 120) + '''''''''' END'
        ELSE IF @data_type = 'date'
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(10), [' + @col_name + '], 120) + '''''''''' END'
        ELSE IF @data_type = 'bit'
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE CAST([' + @col_name + '] AS VARCHAR(1)) END'
        ELSE
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE CAST([' + @col_name + '] AS NVARCHAR(MAX)) END'
        
        FETCH NEXT FROM col_cursor2 INTO @col_name, @data_type
    END
    
    CLOSE col_cursor2
    DEALLOCATE col_cursor2
    
    SET @col_sql = @col_sql + ' + '')'' + CASE WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = (SELECT COUNT(*) FROM SysUser) THEN '''' ELSE '','' END FROM SysUser'
    
    PRINT @col_sql
    PRINT 'ON DUPLICATE KEY UPDATE Id = Id;'
    PRINT ''
END
ELSE
BEGIN
    PRINT '-- 表 SysUser 不存在'
END

-- ========================================
-- 表: SysUserBelong
-- ========================================

PRINT '-- =========================================='
PRINT '-- 表: SysUserBelong'
PRINT '-- =========================================='

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SysUserBelong')
BEGIN
    DECLARE @cols_belong NVARCHAR(MAX)
    SELECT @cols_belong = STUFF((
        SELECT ', ' + COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'SysUserBelong'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 2, '')
    
    PRINT 'INSERT INTO SysUserBelong (' + @cols_belong + ') VALUES'
    
    PRINT '-- 请手动复制以下查询结果:'
    PRINT 'SELECT ''('' + '
    
    SET @col_sql = ''
    SET @first = 1
    
    DECLARE col_cursor3 CURSOR FOR
    SELECT COLUMN_NAME, DATA_TYPE
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'SysUserBelong'
    ORDER BY ORDINAL_POSITION
    
    OPEN col_cursor3
    FETCH NEXT FROM col_cursor3 INTO @col_name, @data_type
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        IF @first = 0
            SET @col_sql = @col_sql + ' + '', '' + '
        ELSE
            SET @first = 0
        
        IF @data_type IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext')
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(CAST([' + @col_name + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''''' END'
        ELSE IF @data_type IN ('datetime', 'datetime2', 'smalldatetime')
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), [' + @col_name + '], 120) + '''''''''' END'
        ELSE IF @data_type = 'date'
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(10), [' + @col_name + '], 120) + '''''''''' END'
        ELSE IF @data_type = 'bit'
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE CAST([' + @col_name + '] AS VARCHAR(1)) END'
        ELSE
            SET @col_sql = @col_sql + 'CASE WHEN [' + @col_name + '] IS NULL THEN ''NULL'' ELSE CAST([' + @col_name + '] AS NVARCHAR(MAX)) END'
        
        FETCH NEXT FROM col_cursor3 INTO @col_name, @data_type
    END
    
    CLOSE col_cursor3
    DEALLOCATE col_cursor3
    
    SET @col_sql = @col_sql + ' + '')'' + CASE WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = (SELECT COUNT(*) FROM SysUserBelong) THEN '''' ELSE '','' END FROM SysUserBelong'
    
    PRINT @col_sql
    PRINT 'ON DUPLICATE KEY UPDATE Id = Id;'
    PRINT ''
END
ELSE
BEGIN
    PRINT '-- 表 SysUserBelong 不存在'
END

PRINT '-- 脚本执行完成'
PRINT '-- 请复制上面生成的SELECT语句到新查询窗口执行，然后复制结果'
