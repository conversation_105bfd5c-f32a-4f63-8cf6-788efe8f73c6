-- 兼容SQL Server 2008+的简单导出脚本
-- 从SQL Server数据库(syjx)导出数据用于MySQL导入

USE syjx;
GO

PRINT '-- ======================================================';
PRINT '-- SQL Server到MySQL数据导出脚本';
PRINT '-- 兼容SQL Server 2008及以上版本';
PRINT '-- 生成时间: ' + CONVERT(VARCHAR(19), GETDATE(), 120);
PRINT '-- ======================================================';
PRINT '';

-- 声明变量
DECLARE @TableName NVARCHAR(128);
DECLARE @SQL NVARCHAR(MAX);
DECLARE @RowCount INT;

-- 获取所有有id字段的表
DECLARE table_cursor CURSOR FOR
SELECT TABLE_NAME
FROM INFORMATION_SCHEMA.TABLES t
WHERE TABLE_TYPE = 'BASE TABLE'
AND EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS c
    WHERE c.TABLE_NAME = t.TABLE_NAME 
    AND c.COLUMN_NAME = 'id'
    AND c.DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint')
)
ORDER BY TABLE_NAME;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '';
    PRINT '-- ======================================================';
    PRINT '-- 表: ' + @TableName;
    PRINT '-- ======================================================';
    
    -- 显示表的记录数
    SET @SQL = 'SELECT @count = COUNT(*) FROM ' + @TableName;
    EXEC sp_executesql @SQL, N'@count INT OUTPUT', @count = @RowCount OUTPUT;
    PRINT '-- 记录数: ' + CAST(@RowCount AS VARCHAR(10));
    PRINT '';
    
    -- 显示表结构
    PRINT '-- 表结构:';
    PRINT 'SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, CHARACTER_MAXIMUM_LENGTH';
    PRINT 'FROM INFORMATION_SCHEMA.COLUMNS';
    PRINT 'WHERE TABLE_NAME = ''' + @TableName + '''';
    PRINT 'ORDER BY ORDINAL_POSITION;';
    PRINT '';
    
    -- 生成简单的数据导出SQL
    PRINT '-- 导出数据SQL (复制结果用于MySQL):';
    PRINT 'SELECT * FROM ' + @TableName + ' ORDER BY id;';
    PRINT '';
    
    -- 生成MySQL表创建语句模板
    PRINT '-- MySQL建表语句模板:';
    PRINT 'CREATE TABLE IF NOT EXISTS `' + @TableName + '` (';
    PRINT '  -- 根据上面的表结构信息手动创建字段';
    PRINT '  `id` INT PRIMARY KEY,';
    PRINT '  -- 其他字段...';
    PRINT ');';
    PRINT '';
    
    -- 生成MySQL插入语句模板
    PRINT '-- MySQL插入语句模板:';
    PRINT 'INSERT IGNORE INTO `' + @TableName + '`';
    PRINT 'SELECT * FROM (';
    PRINT '  -- 将上面的查询结果粘贴到这里';
    PRINT '  -- 格式: SELECT col1, col2, col3, ... UNION ALL';
    PRINT '  --       SELECT col1, col2, col3, ... UNION ALL';
    PRINT '  --       SELECT col1, col2, col3, ...  (最后一行不要UNION ALL)';
    PRINT ') AS temp_data;';
    PRINT '';
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '';
PRINT '-- ======================================================';
PRINT '-- 使用说明:';
PRINT '-- 1. 执行上面的SELECT语句在SQL Server中查看数据';
PRINT '-- 2. 复制查询结果';
PRINT '-- 3. 在MySQL中先创建对应的表结构';
PRINT '-- 4. 使用INSERT IGNORE语句导入数据(自动跳过重复id)';
PRINT '-- 5. INSERT IGNORE会忽略主键冲突，确保不重复插入';
PRINT '-- ======================================================';
GO
