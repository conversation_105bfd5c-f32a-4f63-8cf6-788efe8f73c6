-- 生成MySQL INSERT语句的SQL Server脚本
-- 从SQL Server数据库(syjx)生成可直接在MySQL中执行的INSERT语句
-- 自动处理id重复检查

USE syjx;
GO

-- 声明变量
DECLARE @TableName NVARCHAR(128);
DECLARE @SQL NVARCHAR(MAX);
DECLARE @MySqlScript NVARCHAR(MAX);

PRINT '-- ======================================================';
PRINT '-- MySQL数据迁移脚本';
PRINT '-- 生成时间: ' + CONVERT(VARCHAR(19), GETDATE(), 120);
PRINT '-- 源数据库: SQL Server syjx';
PRINT '-- 目标数据库: MySQL syjx';
PRINT '-- ======================================================';
PRINT '';

-- 获取当前数据库中的所有表
DECLARE table_cursor CURSOR FOR
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    -- 检查表是否有id字段
    IF EXISTS (
        SELECT 1 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = @TableName 
        AND COLUMN_NAME = 'id'
        AND DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint')
    )
    BEGIN
        PRINT '-- ======================================================';
        PRINT '-- 表: ' + @TableName;
        PRINT '-- ======================================================';
        
        -- 动态构建INSERT语句
        SET @SQL = '
DECLARE @MySqlInsert NVARCHAR(MAX) = '''';
DECLARE @Values NVARCHAR(MAX);
DECLARE @ColumnList NVARCHAR(MAX);

-- 获取列列表
SELECT @ColumnList = STRING_AGG(COLUMN_NAME, '', '')
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = ''' + @TableName + '''
ORDER BY ORDINAL_POSITION;

-- 生成INSERT语句头部
SET @MySqlInsert = ''INSERT INTO `' + @TableName + '` ('' + @ColumnList + '') VALUES'';

-- 生成VALUES部分
DECLARE data_cursor CURSOR FOR
SELECT ';

        -- 动态构建SELECT语句
        SELECT @SQL = @SQL + '
    ''('' + ' + STRING_AGG(
            CASE 
                WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                    'ISNULL('''''''' + REPLACE(REPLACE(REPLACE(CAST(' + COLUMN_NAME + ' AS NVARCHAR(MAX)), '''''''', ''''''''''''), CHAR(13), '' ''), CHAR(10), '' '') + '''''''', ''NULL'')'
                WHEN DATA_TYPE IN ('datetime', 'datetime2', 'date') THEN
                    'ISNULL('''''''' + CONVERT(VARCHAR(19), ' + COLUMN_NAME + ', 120) + '''''''', ''NULL'')'
                WHEN DATA_TYPE IN ('time') THEN
                    'ISNULL('''''''' + CONVERT(VARCHAR(8), ' + COLUMN_NAME + ', 108) + '''''''', ''NULL'')'
                WHEN DATA_TYPE IN ('bit') THEN
                    'CAST(' + COLUMN_NAME + ' AS VARCHAR(1))'
                WHEN DATA_TYPE IN ('decimal', 'numeric', 'money', 'smallmoney', 'float', 'real') THEN
                    'ISNULL(CAST(' + COLUMN_NAME + ' AS VARCHAR(50)), ''NULL'')'
                ELSE
                    'ISNULL(CAST(' + COLUMN_NAME + ' AS VARCHAR(50)), ''NULL'')'
            END, ' + '', '' + '
        ) + ' + '')'' AS ValueRow'
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @TableName
        ORDER BY ORDINAL_POSITION;

        SET @SQL = @SQL + '
FROM ' + @TableName + '
ORDER BY id;

OPEN data_cursor;
FETCH NEXT FROM data_cursor INTO @Values;

DECLARE @IsFirst BIT = 1;
WHILE @@FETCH_STATUS = 0
BEGIN
    IF @IsFirst = 1
    BEGIN
        SET @MySqlInsert = @MySqlInsert + CHAR(13) + ''    '' + @Values;
        SET @IsFirst = 0;
    END
    ELSE
    BEGIN
        SET @MySqlInsert = @MySqlInsert + '','' + CHAR(13) + ''    '' + @Values;
    END
    
    FETCH NEXT FROM data_cursor INTO @Values;
END

CLOSE data_cursor;
DEALLOCATE data_cursor;

-- 添加ON DUPLICATE KEY UPDATE子句
SET @MySqlInsert = @MySqlInsert + CHAR(13) + ''ON DUPLICATE KEY UPDATE id=id;'';

-- 输出最终的MySQL INSERT语句
PRINT @MySqlInsert;
PRINT '''';';

        -- 执行动态SQL
        EXEC sp_executesql @SQL;
        
    END
    ELSE
    BEGIN
        PRINT '-- 跳过表 ' + @TableName + '（没有id字段）';
        PRINT '';
    END
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '-- ======================================================';
PRINT '-- 脚本生成完成！';
PRINT '-- 使用说明：';
PRINT '-- 1. 复制上面生成的INSERT语句';
PRINT '-- 2. 在MySQL中执行这些语句';
PRINT '-- 3. ON DUPLICATE KEY UPDATE确保不会插入重复的id';
PRINT '-- ======================================================';
GO
