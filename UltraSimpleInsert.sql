-- 超简单版本：生成三个表的INSERT语句
-- 直接执行，每条数据生成一个INSERT语句

-- ========================================
-- 第一步：查看表结构，确认列名
-- ========================================

-- 查看 bn_Attachment 表结构
SELECT 'bn_Attachment 表结构:' AS Info
SELECT COLUMN_NAME, DATA_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'bn_Attachment' 
ORDER BY ORDINAL_POSITION

-- 查看 SysUser 表结构  
SELECT 'SysUser 表结构:' AS Info
SELECT COLUMN_NAME, DATA_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'SysUser' 
ORDER BY ORDINAL_POSITION

-- 查看 SysUserBelong 表结构
SELECT 'SysUserBelong 表结构:' AS Info
SELECT COLUMN_NAME, DATA_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'SysUserBelong' 
ORDER BY ORDINAL_POSITION

-- ========================================
-- 第二步：生成INSERT语句（请根据上面的列名调整）
-- ========================================

-- bn_Attachment 表（请根据实际列名修改）
PRINT '-- bn_Attachment 表的INSERT语句:'
SELECT 
    'INSERT INTO bn_Attachment VALUES (' +
    CAST(ISNULL([列名1], 'NULL') AS NVARCHAR(MAX)) + ', ' +
    CASE WHEN [列名2] IS NULL THEN 'NULL' ELSE '''' + REPLACE([列名2], '''', '''''') + '''' END + ', ' +
    -- 继续添加其他列...
    ') ON DUPLICATE KEY UPDATE Id = Id;'
FROM bn_Attachment

-- 通用模板 - bn_Attachment
PRINT '-- 请复制以下查询并根据实际列名修改后执行:'
PRINT 'SELECT ''INSERT INTO bn_Attachment ('' + '
PRINT '    (SELECT STUFF(('
PRINT '        SELECT '', '' + COLUMN_NAME'
PRINT '        FROM INFORMATION_SCHEMA.COLUMNS'
PRINT '        WHERE TABLE_NAME = ''bn_Attachment'''
PRINT '        ORDER BY ORDINAL_POSITION'
PRINT '        FOR XML PATH('''')'
PRINT '    ), 1, 2, '''')) + '') VALUES ('' +'
PRINT '    -- 这里需要手动添加每列的值转换'
PRINT '    '') ON DUPLICATE KEY UPDATE Id = Id;'''
PRINT 'FROM bn_Attachment'
PRINT ''

-- 通用模板 - SysUser  
PRINT '-- SysUser 表:'
PRINT 'SELECT ''INSERT INTO SysUser ('' + '
PRINT '    (SELECT STUFF(('
PRINT '        SELECT '', '' + COLUMN_NAME'
PRINT '        FROM INFORMATION_SCHEMA.COLUMNS'
PRINT '        WHERE TABLE_NAME = ''SysUser'''
PRINT '        ORDER BY ORDINAL_POSITION'
PRINT '        FOR XML PATH('''')'
PRINT '    ), 1, 2, '''')) + '') VALUES ('' +'
PRINT '    -- 这里需要手动添加每列的值转换'
PRINT '    '') ON DUPLICATE KEY UPDATE Id = Id;'''
PRINT 'FROM SysUser'
PRINT ''

-- 通用模板 - SysUserBelong
PRINT '-- SysUserBelong 表:'
PRINT 'SELECT ''INSERT INTO SysUserBelong ('' + '
PRINT '    (SELECT STUFF(('
PRINT '        SELECT '', '' + COLUMN_NAME'
PRINT '        FROM INFORMATION_SCHEMA.COLUMNS'
PRINT '        WHERE TABLE_NAME = ''SysUserBelong'''
PRINT '        ORDER BY ORDINAL_POSITION'
PRINT '        FOR XML PATH('''')'
PRINT '    ), 1, 2, '''')) + '') VALUES ('' +'
PRINT '    -- 这里需要手动添加每列的值转换'
PRINT '    '') ON DUPLICATE KEY UPDATE Id = Id;'''
PRINT 'FROM SysUserBelong'

-- ========================================
-- 第三步：简化版本（假设常见列名）
-- ========================================

-- 假设 bn_Attachment 有这些列：Id, FileName, FilePath, FileSize, CreateTime
PRINT ''
PRINT '-- 假设列名的示例（请根据实际情况修改）:'
PRINT ''

PRINT '-- bn_Attachment 示例:'
PRINT 'SELECT '
PRINT '    ''INSERT INTO bn_Attachment (Id, FileName, FilePath, FileSize, CreateTime) VALUES ('' +'
PRINT '    CAST(Id AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CASE WHEN FileName IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(FileName, '''''''', '''''''''''') + '''''''''' END + '', '' +'
PRINT '    CASE WHEN FilePath IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(FilePath, '''''''', '''''''''''') + '''''''''' END + '', '' +'
PRINT '    CAST(ISNULL(FileSize, 0) AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CASE WHEN CreateTime IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), CreateTime, 120) + '''''''''' END +'
PRINT '    '') ON DUPLICATE KEY UPDATE Id = Id;'''
PRINT 'FROM bn_Attachment'
PRINT ''

PRINT '-- SysUser 示例:'
PRINT 'SELECT '
PRINT '    ''INSERT INTO SysUser (Id, UserName, Password, Email, CreateTime) VALUES ('' +'
PRINT '    CAST(Id AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CASE WHEN UserName IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(UserName, '''''''', '''''''''''') + '''''''''' END + '', '' +'
PRINT '    CASE WHEN Password IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(Password, '''''''', '''''''''''') + '''''''''' END + '', '' +'
PRINT '    CASE WHEN Email IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(Email, '''''''', '''''''''''') + '''''''''' END + '', '' +'
PRINT '    CASE WHEN CreateTime IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), CreateTime, 120) + '''''''''' END +'
PRINT '    '') ON DUPLICATE KEY UPDATE Id = Id;'''
PRINT 'FROM SysUser'
PRINT ''

PRINT '-- SysUserBelong 示例:'
PRINT 'SELECT '
PRINT '    ''INSERT INTO SysUserBelong (Id, UserId, RoleId, CreateTime) VALUES ('' +'
PRINT '    CAST(Id AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CAST(UserId AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CAST(RoleId AS NVARCHAR(MAX)) + '', '' +'
PRINT '    CASE WHEN CreateTime IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), CreateTime, 120) + '''''''''' END +'
PRINT '    '') ON DUPLICATE KEY UPDATE Id = Id;'''
PRINT 'FROM SysUserBelong'

PRINT ''
PRINT '-- 使用说明：'
PRINT '-- 1. 先执行第一步查看表结构'
PRINT '-- 2. 根据实际列名修改第三步的示例查询'
PRINT '-- 3. 执行修改后的查询，复制结果即为INSERT语句'
