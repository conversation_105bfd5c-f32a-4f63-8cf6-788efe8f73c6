# 数据迁移工具

## 概述
这是一套用于从Hyun数据库迁移数据到HyunCoreBase数据库的工具集。主要功能包括：
- 迁移三个表：`dc_PurchaseList`、`dc_PurchaseOrder`、`dc_PurchaseApproval`
- 自动处理用户ID和单位ID的替换
- 智能处理缺失字段，使用合适的默认值
- 避免重复数据插入

## 配置信息
- **数据库服务器**: 192.168.2.3
- **源数据库**: Hyun
- **目标数据库**: HyunCoreBase
- **用户名**: hyun
- **密码**: hyun
- **固定用户ID**: 592123693924485
- **固定单位ID**: 592123124052101

## 文件说明

### 1. FieldMappingMigration.sql ⭐ **推荐使用**
**字段映射迁移脚本**
- 专门处理源表字段少于目标表的情况
- 明确的字段映射关系，易于理解和维护
- 对缺失字段使用合适的默认值
- 包含详细的错误处理和日志输出
- 显示源表结构信息，便于调试

### 2. SmartDataMigration.sql
**智能版迁移脚本**
- 动态分析表结构差异
- 自动检测源表和目标表的字段差异
- 智能生成INSERT语句
- 更灵活，适应表结构变化
- 适合表结构经常变化的场景

### 3. AnalyzeTableDifferences.sql
**表结构差异分析工具**
- 详细对比源表和目标表的字段
- 显示哪些字段需要使用默认值
- 统计字段差异信息
- 帮助理解迁移过程中的字段处理
- 建议在迁移前先运行此脚本

### 4. SimpleInsertMigration.sql
**简化版迁移脚本**
- 动态构建INSERT语句
- 使用ISNULL函数处理空值
- 为不同数据类型提供合适的默认值

### 5. GenerateInsertScript.sql
**脚本生成工具**
- 分析目标表结构
- 自动生成INSERT INTO SELECT脚本
- 输出可直接执行的SQL代码
- 便于自定义和调试

### 6. ExecuteMigration.bat
**批处理执行工具**
- 提供菜单选择不同的迁移方式
- 自动调用sqlcmd执行SQL脚本
- 显示执行结果和错误信息

## 使用方法

### ⭐ 推荐流程（适合表结构未知的情况）

#### 第一步：查看表结构
1. 双击运行 `ExecuteMigration.bat`
2. 选择 "1. 显示表结构对比"
3. 查看源表和目标表的实际字段结构

#### 第二步：选择迁移方式
根据表结构复杂程度选择：

**简单情况（字段名相似）：**
- 选择 "3. 智能字段映射迁移" 让脚本自动处理

**复杂情况（字段名完全不同）：**
- 复制 `ManualMigrationTemplate.sql` 文件
- 根据第一步看到的实际字段结构手动修改INSERT语句
- 在SQL Server Management Studio中执行修改后的脚本

### 方法一：自动化迁移（推荐尝试）
1. 双击运行 `ExecuteMigration.bat`
2. 选择 "1. 显示表结构对比" 了解字段
3. 选择 "3. 智能字段映射迁移" 尝试自动迁移
4. 如果失败，则使用手动方式

### 方法二：手动迁移（最可靠）
1. 运行 `ShowTableStructure.sql` 查看实际表结构
2. 复制 `ManualMigrationTemplate.sql` 并重命名
3. 根据实际字段修改INSERT语句
4. 执行修改后的脚本

### 方法三：生成脚本模板
1. 双击运行 `ExecuteMigration.bat`
2. 选择 "2. 检查并生成脚本"
3. 复制生成的INSERT语句模板
4. 根据实际情况调整后执行

## 默认值规则

脚本会根据字段的数据类型自动分配默认值：

| 数据类型 | 默认值 |
|---------|--------|
| int, bigint, smallint, tinyint, bit | 0 |
| decimal, numeric, float, real, money, smallmoney | 0.0 |
| varchar, nvarchar, char, nchar, text, ntext | '' (空字符串) |
| datetime, datetime2, smalldatetime, date, time | GETDATE() |
| uniqueidentifier | NEWID() |
| 其他类型 | NULL 或 '' |

## 特殊字段处理

### 用户ID字段
以下字段会被替换为固定值 `592123693924485`：
- 包含 "UserId" 的字段
- CreateUserId
- UpdateUserId
- CreatedBy
- UpdatedBy

### 单位ID字段
以下字段会被替换为固定值 `592123124052101`：
- 包含 "UnitId" 的字段
- OrganizationId
- DepartmentId

## 注意事项

1. **备份数据**：执行迁移前请备份目标数据库
2. **权限检查**：确保数据库用户有足够的权限
3. **网络连接**：确保能够连接到数据库服务器
4. **表结构**：确保目标表已存在且结构正确
5. **重复执行**：脚本使用NOT EXISTS避免重复插入，可以安全重复执行

## 故障排除

### 常见错误
1. **连接失败**：检查服务器地址、用户名、密码
2. **表不存在**：确认源表和目标表都存在
3. **权限不足**：确保用户有SELECT和INSERT权限
4. **数据类型不匹配**：检查表结构是否一致

### 调试方法
1. 使用 `GenerateInsertScript.sql` 生成脚本并检查
2. 先在测试环境执行
3. 逐个表执行，而不是批量执行

## 扩展使用

如果需要迁移其他表，可以：
1. 修改脚本中的表名列表
2. 调整特殊字段的处理规则
3. 根据需要修改默认值规则

## 技术支持

如有问题，请检查：
1. SQL Server版本兼容性
2. 数据库连接配置
3. 表结构差异
4. 数据类型兼容性
