-- 最简单版本：生成三个表的INSERT语句
-- 每条数据一个INSERT语句

-- ========================================
-- bn_Attachment 表
-- ========================================

-- 复制以下查询并执行：
SELECT 
    'INSERT INTO bn_Attachment (' + 
    STUFF((
        SELECT ', ' + COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'bn_Attachment'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 2, '') + 
    ') VALUES (' +
    STUFF((
        SELECT ', ' + 
            CASE 
                WHEN c.DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                    CASE WHEN v.val IS NULL THEN 'NULL' 
                         ELSE '''' + REPLACE(CAST(v.val AS NVARCHAR(MAX)), '''', '''''') + '''' 
                    END
                WHEN c.DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
                    CASE WHEN v.val IS NULL THEN 'NULL' 
                         ELSE '''' + CONVERT(VARCHAR(19), CAST(v.val AS DATETIME), 120) + '''' 
                    END
                WHEN c.DATA_TYPE = 'date' THEN
                    CASE WHEN v.val IS NULL THEN 'NULL' 
                         ELSE '''' + CONVERT(VARCHAR(10), CAST(v.val AS DATE), 120) + '''' 
                    END
                WHEN c.DATA_TYPE = 'bit' THEN
                    CASE WHEN v.val IS NULL THEN 'NULL' 
                         ELSE CAST(v.val AS VARCHAR(1)) 
                    END
                ELSE 
                    CASE WHEN v.val IS NULL THEN 'NULL' 
                         ELSE CAST(v.val AS NVARCHAR(MAX)) 
                    END
            END
        FROM INFORMATION_SCHEMA.COLUMNS c
        CROSS APPLY (
            SELECT 
                CASE c.ORDINAL_POSITION
                    WHEN 1 THEN (SELECT TOP 1 CAST([Id] AS SQL_VARIANT) FROM bn_Attachment WHERE [Id] = t.[Id])
                    -- 这里需要手动添加每个列
                END AS val
        ) v
        WHERE c.TABLE_NAME = 'bn_Attachment'
        ORDER BY c.ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 2, '') + 
    ') ON DUPLICATE KEY UPDATE Id = Id;' AS InsertStatement
FROM bn_Attachment t

-- ========================================
-- 更简单的方法：手动构建
-- ========================================

-- bn_Attachment 表 - 请根据实际列名调整
SELECT 
    'INSERT INTO bn_Attachment (Id, FileName, FilePath, FileSize, CreateTime) VALUES (' +
    CASE WHEN Id IS NULL THEN 'NULL' ELSE CAST(Id AS NVARCHAR(MAX)) END + ', ' +
    CASE WHEN FileName IS NULL THEN 'NULL' ELSE '''' + REPLACE(FileName, '''', '''''') + '''' END + ', ' +
    CASE WHEN FilePath IS NULL THEN 'NULL' ELSE '''' + REPLACE(FilePath, '''', '''''') + '''' END + ', ' +
    CASE WHEN FileSize IS NULL THEN 'NULL' ELSE CAST(FileSize AS NVARCHAR(MAX)) END + ', ' +
    CASE WHEN CreateTime IS NULL THEN 'NULL' ELSE '''' + CONVERT(VARCHAR(19), CreateTime, 120) + '''' END +
    ') ON DUPLICATE KEY UPDATE Id = Id;' AS InsertStatement
FROM bn_Attachment

-- SysUser 表 - 请根据实际列名调整
SELECT 
    'INSERT INTO SysUser (Id, UserName, Password, Email, CreateTime) VALUES (' +
    CASE WHEN Id IS NULL THEN 'NULL' ELSE CAST(Id AS NVARCHAR(MAX)) END + ', ' +
    CASE WHEN UserName IS NULL THEN 'NULL' ELSE '''' + REPLACE(UserName, '''', '''''') + '''' END + ', ' +
    CASE WHEN Password IS NULL THEN 'NULL' ELSE '''' + REPLACE(Password, '''', '''''') + '''' END + ', ' +
    CASE WHEN Email IS NULL THEN 'NULL' ELSE '''' + REPLACE(Email, '''', '''''') + '''' END + ', ' +
    CASE WHEN CreateTime IS NULL THEN 'NULL' ELSE '''' + CONVERT(VARCHAR(19), CreateTime, 120) + '''' END +
    ') ON DUPLICATE KEY UPDATE Id = Id;' AS InsertStatement
FROM SysUser

-- SysUserBelong 表 - 请根据实际列名调整
SELECT 
    'INSERT INTO SysUserBelong (Id, UserId, RoleId, CreateTime) VALUES (' +
    CASE WHEN Id IS NULL THEN 'NULL' ELSE CAST(Id AS NVARCHAR(MAX)) END + ', ' +
    CASE WHEN UserId IS NULL THEN 'NULL' ELSE CAST(UserId AS NVARCHAR(MAX)) END + ', ' +
    CASE WHEN RoleId IS NULL THEN 'NULL' ELSE CAST(RoleId AS NVARCHAR(MAX)) END + ', ' +
    CASE WHEN CreateTime IS NULL THEN 'NULL' ELSE '''' + CONVERT(VARCHAR(19), CreateTime, 120) + '''' END +
    ') ON DUPLICATE KEY UPDATE Id = Id;' AS InsertStatement
FROM SysUserBelong
