# PowerShell Script: Auto export SQL Server data to MySQL script files
# Generate independent MySQL INSERT script file for each table

param(
    [string]$ServerName = "***********",
    [string]$DatabaseName = "syjx",
    [string]$Username = "hyun",
    [string]$Password = "hyun",
    [string]$OutputPath = ".\mysql_scripts"
)

Write-Host "Starting auto export from SQL Server to MySQL scripts..." -ForegroundColor Green
Write-Host "Server: $ServerName" -ForegroundColor Yellow
Write-Host "Database: $DatabaseName" -ForegroundColor Yellow
Write-Host "Output Path: $OutputPath" -ForegroundColor Yellow
Write-Host ""

# Create output directory
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Host "Created output directory: $OutputPath" -ForegroundColor Green
}

try {
    # Build connection string
    $connectionString = "Server=$ServerName;Database=$DatabaseName;User Id=$Username;Password=$Password;TrustServerCertificate=true;"

    # Create SQL connection
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()

    Write-Host "Connected to SQL Server: $ServerName" -ForegroundColor Green

    # Get all tables with id field
    $getTablesQuery = @"
SELECT t.TABLE_NAME,
       (SELECT COUNT(*)
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = t.TABLE_NAME) as ColumnCount
FROM INFORMATION_SCHEMA.TABLES t
WHERE t.TABLE_TYPE = 'BASE TABLE'
AND EXISTS (
    SELECT 1
    FROM INFORMATION_SCHEMA.COLUMNS c
    WHERE c.TABLE_NAME = t.TABLE_NAME
    AND c.COLUMN_NAME = 'id'
    AND c.DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint')
)
ORDER BY t.TABLE_NAME
"@
    
    $command = New-Object System.Data.SqlClient.SqlCommand($getTablesQuery, $connection)
    $reader = $command.ExecuteReader()

    $tables = @()
    while ($reader.Read()) {
        $tables += @{
            Name = $reader.GetString("TABLE_NAME")
            ColumnCount = $reader.GetInt32("ColumnCount")
        }
    }
    $reader.Close()

    Write-Host "Found $($tables.Count) tables with id field" -ForegroundColor Cyan
    Write-Host ""
    
    foreach ($table in $tables) {
        $tableName = $table.Name
        Write-Host "Processing table: $tableName" -ForegroundColor Yellow

        # Check if table has data
        $countQuery = "SELECT COUNT(*) FROM [$tableName]"
        $countCommand = New-Object System.Data.SqlClient.SqlCommand($countQuery, $connection)
        $rowCount = $countCommand.ExecuteScalar()

        if ($rowCount -gt 0) {
            Write-Host "  Row count: $rowCount" -ForegroundColor Cyan

            # Get column information
            $columnsQuery = @"
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = '$tableName'
ORDER BY ORDINAL_POSITION
"@

            $columnsCommand = New-Object System.Data.SqlClient.SqlCommand($columnsQuery, $connection)
            $columnsReader = $columnsCommand.ExecuteReader()

            $columns = @()
            while ($columnsReader.Read()) {
                $columns += @{
                    Name = $columnsReader.GetString("COLUMN_NAME")
                    DataType = $columnsReader.GetString("DATA_TYPE")
                    IsNullable = $columnsReader.GetString("IS_NULLABLE")
                    MaxLength = if ($columnsReader.IsDBNull("CHARACTER_MAXIMUM_LENGTH")) { $null } else { $columnsReader.GetInt32("CHARACTER_MAXIMUM_LENGTH") }
                }
            }
            $columnsReader.Close()
            
            # Generate MySQL script content
            $currentDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            $mysqlScript = "-- MySQL Insert Script - Table: $tableName`r`n"
            $mysqlScript += "-- Generated: $currentDate`r`n"
            $mysqlScript += "-- Row Count: $rowCount`r`n"
            $mysqlScript += "-- Note: Using INSERT IGNORE to skip duplicate ids`r`n"
            $mysqlScript += "`r`n"

            # Generate column names list
            $columnNames = ($columns | ForEach-Object { "``$($_.Name)``" }) -join ", "
            
            # Get actual data
            $dataQuery = "SELECT * FROM [$tableName] ORDER BY id"
            $dataCommand = New-Object System.Data.SqlClient.SqlCommand($dataQuery, $connection)
            $dataReader = $dataCommand.ExecuteReader()

            $values = @()
            while ($dataReader.Read()) {
                $rowValues = @()
                for ($i = 0; $i -lt $columns.Count; $i++) {
                    $column = $columns[$i]
                    $value = $dataReader[$i]

                    if ($dataReader.IsDBNull($i)) {
                        $rowValues += "NULL"
                    } else {
                        switch ($column.DataType) {
                            { $_ -in @('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') } {
                                $escapedValue = $value.ToString().Replace("'", "''").Replace("`r", "").Replace("`n", " ")
                                $rowValues += "'$escapedValue'"
                            }
                            { $_ -in @('datetime', 'datetime2', 'smalldatetime') } {
                                $dateValue = [DateTime]$value
                                $rowValues += "'$($dateValue.ToString("yyyy-MM-dd HH:mm:ss"))'"
                            }
                            { $_ -eq 'date' } {
                                $dateValue = [DateTime]$value
                                $rowValues += "'$($dateValue.ToString("yyyy-MM-dd"))'"
                            }
                            { $_ -eq 'time' } {
                                $timeValue = [TimeSpan]$value
                                $rowValues += "'$($timeValue.ToString("hh\:mm\:ss"))'"
                            }
                            { $_ -eq 'bit' } {
                                $rowValues += if ($value) { "1" } else { "0" }
                            }
                            default {
                                $rowValues += $value.ToString()
                            }
                        }
                    }
                }
                $values += "(" + ($rowValues -join ", ") + ")"
            }
            $dataReader.Close()
            
            # Complete MySQL script
            $mysqlScript += "INSERT IGNORE INTO ``$tableName`` ($columnNames) VALUES`r`n"
            $mysqlScript += ($values -join ",`r`n") + ";"

            # Save to file
            $fileName = "$($tableName)_mysql_insert.sql"
            $filePath = Join-Path $OutputPath $fileName
            $mysqlScript | Out-File -FilePath $filePath -Encoding UTF8

            Write-Host "  Generated: $fileName" -ForegroundColor Green

        } else {
            Write-Host "  Skipping empty table" -ForegroundColor Gray
        }
    }

    Write-Host ""
    Write-Host "======================================" -ForegroundColor Green
    Write-Host "Export completed!" -ForegroundColor Green
    Write-Host "Generated files location: $OutputPath" -ForegroundColor Green
    Write-Host "Total processed tables: $($tables.Count)" -ForegroundColor Green
    Write-Host "======================================" -ForegroundColor Green

} catch {
    Write-Host "Error occurred:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
} finally {
    # Close connection
    if ($connection -and $connection.State -eq 'Open') {
        $connection.Close()
        Write-Host "Database connection closed" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
