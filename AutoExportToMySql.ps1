# PowerShell脚本：自动从SQL Server导出数据并生成MySQL脚本文件
# 每个表生成一个独立的MySQL INSERT脚本文件

param(
    [string]$ServerName = "***********",
    [string]$DatabaseName = "syjx",
    [string]$Username = "hyun",
    [string]$Password = "hyun",
    [string]$OutputPath = ".\mysql_scripts"
)

Write-Host "开始自动导出SQL Server数据到MySQL脚本..." -ForegroundColor Green
Write-Host "服务器: $ServerName" -ForegroundColor Yellow
Write-Host "数据库: $DatabaseName" -ForegroundColor Yellow
Write-Host "输出目录: $OutputPath" -ForegroundColor Yellow
Write-Host ""

# 创建输出目录
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Host "创建输出目录: $OutputPath" -ForegroundColor Green
}

try {
    # 构建连接字符串
    $connectionString = "Server=$ServerName;Database=$DatabaseName;User Id=$Username;Password=$Password;TrustServerCertificate=true;"
    
    # 创建SQL连接
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "已连接到SQL Server: $ServerName" -ForegroundColor Green
    
    # 获取所有有id字段的表
    $getTablesQuery = @"
SELECT t.TABLE_NAME, 
       (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = t.TABLE_NAME) as ColumnCount
FROM INFORMATION_SCHEMA.TABLES t
WHERE t.TABLE_TYPE = 'BASE TABLE'
AND EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS c
    WHERE c.TABLE_NAME = t.TABLE_NAME 
    AND c.COLUMN_NAME = 'id'
    AND c.DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint')
)
ORDER BY t.TABLE_NAME
"@
    
    $command = New-Object System.Data.SqlClient.SqlCommand($getTablesQuery, $connection)
    $reader = $command.ExecuteReader()
    
    $tables = @()
    while ($reader.Read()) {
        $tables += @{
            Name = $reader["TABLE_NAME"].ToString()
            ColumnCount = $reader["ColumnCount"]
        }
    }
    $reader.Close()
    
    Write-Host "找到 $($tables.Count) 个包含id字段的表" -ForegroundColor Cyan
    Write-Host ""
    
    foreach ($table in $tables) {
        $tableName = $table.Name
        Write-Host "处理表: $tableName" -ForegroundColor Yellow
        
        # 检查表是否有数据
        $countQuery = "SELECT COUNT(*) FROM [$tableName]"
        $countCommand = New-Object System.Data.SqlClient.SqlCommand($countQuery, $connection)
        $rowCount = $countCommand.ExecuteScalar()
        
        if ($rowCount -gt 0) {
            Write-Host "  记录数: $rowCount" -ForegroundColor Cyan
            
            # 获取列信息
            $columnsQuery = @"
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = '$tableName'
ORDER BY ORDINAL_POSITION
"@
            
            $columnsCommand = New-Object System.Data.SqlClient.SqlCommand($columnsQuery, $connection)
            $columnsReader = $columnsCommand.ExecuteReader()
            
            $columns = @()
            while ($columnsReader.Read()) {
                $columns += @{
                    Name = $columnsReader["COLUMN_NAME"].ToString()
                    DataType = $columnsReader["DATA_TYPE"].ToString()
                    IsNullable = $columnsReader["IS_NULLABLE"].ToString()
                    MaxLength = if ($columnsReader["CHARACTER_MAXIMUM_LENGTH"] -eq [DBNull]::Value) { $null } else { $columnsReader["CHARACTER_MAXIMUM_LENGTH"] }
                }
            }
            $columnsReader.Close()
            
            # 生成MySQL脚本内容
            $mysqlScript = @"
-- MySQL插入脚本 - 表: $tableName
-- 生成时间: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
-- 记录数: $rowCount
-- 说明: 使用INSERT IGNORE自动跳过重复的id

"@
            
            # 生成列名列表
            $columnNames = ($columns | ForEach-Object { "``$($_.Name)``" }) -join ", "
            
            # 获取实际数据
            $dataQuery = "SELECT * FROM [$tableName] ORDER BY id"
            $dataCommand = New-Object System.Data.SqlClient.SqlCommand($dataQuery, $connection)
            $dataReader = $dataCommand.ExecuteReader()
            
            $values = @()
            while ($dataReader.Read()) {
                $rowValues = @()
                for ($i = 0; $i -lt $columns.Count; $i++) {
                    $column = $columns[$i]
                    $value = $dataReader[$i]
                    
                    if ($value -eq [DBNull]::Value) {
                        $rowValues += "NULL"
                    } else {
                        switch ($column.DataType) {
                            { $_ -in @('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') } {
                                $escapedValue = $value.ToString().Replace("'", "''").Replace("`r", "").Replace("`n", " ")
                                $rowValues += "'$escapedValue'"
                            }
                            { $_ -in @('datetime', 'datetime2', 'smalldatetime') } {
                                $dateValue = [DateTime]$value
                                $rowValues += "'$($dateValue.ToString("yyyy-MM-dd HH:mm:ss"))'"
                            }
                            { $_ -eq 'date' } {
                                $dateValue = [DateTime]$value
                                $rowValues += "'$($dateValue.ToString("yyyy-MM-dd"))'"
                            }
                            { $_ -eq 'time' } {
                                $timeValue = [TimeSpan]$value
                                $rowValues += "'$($timeValue.ToString("hh\:mm\:ss"))'"
                            }
                            { $_ -eq 'bit' } {
                                $rowValues += if ($value) { "1" } else { "0" }
                            }
                            default {
                                $rowValues += $value.ToString()
                            }
                        }
                    }
                }
                $values += "(" + ($rowValues -join ", ") + ")"
            }
            $dataReader.Close()
            
            # 完成MySQL脚本
            $mysqlScript += "INSERT IGNORE INTO ``$tableName`` ($columnNames) VALUES`r`n"
            $mysqlScript += ($values -join ",`r`n") + ";"
            
            # 保存到文件
            $fileName = "$($tableName)_mysql_insert.sql"
            $filePath = Join-Path $OutputPath $fileName
            $mysqlScript | Out-File -FilePath $filePath -Encoding UTF8
            
            Write-Host "  已生成: $fileName" -ForegroundColor Green
            
        } else {
            Write-Host "  跳过空表" -ForegroundColor Gray
        }
    }
    
    Write-Host ""
    Write-Host "======================================" -ForegroundColor Green
    Write-Host "导出完成！" -ForegroundColor Green
    Write-Host "生成的文件位于: $OutputPath" -ForegroundColor Green
    Write-Host "总共处理了 $($tables.Count) 个表" -ForegroundColor Green
    Write-Host "======================================" -ForegroundColor Green
    
} catch {
    Write-Host "发生错误:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
} finally {
    # 关闭连接
    if ($connection -and $connection.State -eq 'Open') {
        $connection.Close()
        Write-Host "数据库连接已关闭" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
