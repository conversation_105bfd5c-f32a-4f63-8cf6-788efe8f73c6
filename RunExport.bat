@echo off
echo ========================================
echo SQL Server to MySQL Export Tool
echo ========================================
echo.
echo Connecting to SQL Server: ***********
echo Database: syjx
echo User: hyun
echo.
echo Generating export script for all tables with id field...
echo.

sqlcmd -S *********** -U hyun -P hyun -d syjx -i ExportToMySqlScript.sql

echo.
echo ========================================
if %ERRORLEVEL% EQU 0 (
    echo Script executed successfully!
    echo.
    echo Next steps:
    echo 1. Copy the SELECT statements from above
    echo 2. Execute them in SQL Server Management Studio
    echo 3. Copy the results and format as MySQL INSERT statements
    echo 4. Execute in MySQL with the provided templates
) else (
    echo Script execution failed! Error code: %ERRORLEVEL%
    echo.
    echo Please check:
    echo 1. SQL Server is running and accessible
    echo 2. Database 'syjx' exists
    echo 3. User 'hyun' has read permissions
    echo 4. File 'ExportToMySqlScript.sql' exists
)
echo ========================================
echo.
pause
