-- 手动迁移模板
-- 请根据实际表结构修改此脚本

USE HyunCoreBase;
GO

DECLARE @FixedUserId NVARCHAR(50) = '592123693924485';
DECLARE @FixedUnitId NVARCHAR(50) = '592123124052101';

PRINT '开始手动数据迁移...';
PRINT '固定用户ID: ' + @FixedUserId;
PRINT '固定单位ID: ' + @FixedUnitId;
PRINT '';

-- ========================================
-- 1. 迁移 dc_PurchaseList 表
-- ========================================
PRINT '正在迁移 dc_PurchaseList 表...';

BEGIN TRY
    -- TODO: 请根据实际表结构修改以下INSERT语句
    -- 步骤：
    -- 1. 运行 ShowTableStructure.sql 查看实际字段
    -- 2. 修改下面的字段列表和SELECT列表
    -- 3. 确保字段数量和类型匹配
    
    /*
    INSERT INTO dc_PurchaseList (
        -- 请在这里列出目标表的实际字段名（排除自增列）
        Id,                    -- 假设字段，请替换为实际字段名
        实际字段名1,
        实际字段名2,
        CreateUserId,          -- 用户ID字段
        UnitId                 -- 单位ID字段
        -- 添加更多字段...
    )
    SELECT 
        -- 请在这里列出对应的源表字段或默认值
        src.Id,                           -- 如果源表有Id字段
        ISNULL(src.实际源字段名1, '默认值'),  -- 根据数据类型设置默认值
        ISNULL(src.实际源字段名2, 0),       -- 数值类型用0
        @FixedUserId,                     -- 固定用户ID
        @FixedUnitId                      -- 固定单位ID
        -- 添加更多字段映射...
    FROM Hyun.dbo.dc_PurchaseList src
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseList target 
        WHERE target.Id = src.Id  -- 假设用Id字段防重复，请根据实际情况调整
    );
    */
    
    -- 示例：如果您的表结构是这样的，请取消注释并修改：
    /*
    INSERT INTO dc_PurchaseList (
        Id, 
        PurchaseNo, 
        Amount, 
        CreateUserId, 
        UnitId
    )
    SELECT 
        src.Id,
        ISNULL(src.PurchaseNo, '') AS PurchaseNo,
        ISNULL(src.Amount, 0) AS Amount,
        @FixedUserId AS CreateUserId,
        @FixedUnitId AS UnitId
    FROM Hyun.dbo.dc_PurchaseList src
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseList target 
        WHERE target.Id = src.Id
    );
    */
    
    PRINT '请修改上面的INSERT语句后再执行！';
    PRINT 'dc_PurchaseList 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseList 表迁移失败: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- ========================================
-- 2. 迁移 dc_PurchaseOrder 表
-- ========================================
PRINT '正在迁移 dc_PurchaseOrder 表...';

BEGIN TRY
    -- TODO: 请根据实际表结构修改以下INSERT语句
    
    /*
    INSERT INTO dc_PurchaseOrder (
        -- 请在这里列出目标表的实际字段名
        Id,
        实际字段名1,
        实际字段名2,
        CreateUserId,
        UnitId
        -- 添加更多字段...
    )
    SELECT 
        -- 请在这里列出对应的源表字段或默认值
        src.Id,
        ISNULL(src.实际源字段名1, '默认值'),
        ISNULL(src.实际源字段名2, 0),
        @FixedUserId,
        @FixedUnitId
        -- 添加更多字段映射...
    FROM Hyun.dbo.dc_PurchaseOrder src
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseOrder target 
        WHERE target.Id = src.Id
    );
    */
    
    PRINT '请修改上面的INSERT语句后再执行！';
    PRINT 'dc_PurchaseOrder 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseOrder 表迁移失败: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- ========================================
-- 3. 迁移 dc_PurchaseApproval 表
-- ========================================
PRINT '正在迁移 dc_PurchaseApproval 表...';

BEGIN TRY
    -- TODO: 请根据实际表结构修改以下INSERT语句
    
    /*
    INSERT INTO dc_PurchaseApproval (
        -- 请在这里列出目标表的实际字段名
        Id,
        实际字段名1,
        实际字段名2,
        CreateUserId,
        UnitId
        -- 添加更多字段...
    )
    SELECT 
        -- 请在这里列出对应的源表字段或默认值
        src.Id,
        ISNULL(src.实际源字段名1, '默认值'),
        ISNULL(src.实际源字段名2, 0),
        @FixedUserId,
        @FixedUnitId
        -- 添加更多字段映射...
    FROM Hyun.dbo.dc_PurchaseApproval src
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseApproval target 
        WHERE target.Id = src.Id
    );
    */
    
    PRINT '请修改上面的INSERT语句后再执行！';
    PRINT 'dc_PurchaseApproval 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseApproval 表迁移失败: ' + ERROR_MESSAGE();
END CATCH

PRINT '';
PRINT '========================================';
PRINT '迁移完成！';
PRINT '========================================';

-- 显示迁移后的统计信息
PRINT '';
PRINT '迁移后统计信息:';
PRINT 'dc_PurchaseList 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseList) AS NVARCHAR(10));
PRINT 'dc_PurchaseOrder 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseOrder) AS NVARCHAR(10));
PRINT 'dc_PurchaseApproval 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseApproval) AS NVARCHAR(10));

PRINT '';
PRINT '使用说明：';
PRINT '1. 先运行 ShowTableStructure.sql 查看实际表结构';
PRINT '2. 根据实际字段名修改上面的INSERT语句';
PRINT '3. 取消注释修改后的INSERT语句';
PRINT '4. 重新运行此脚本';
PRINT '';
PRINT '默认值规则：';
PRINT '- 数值类型字段: 0';
PRINT '- 字符串类型字段: ''''';
PRINT '- 日期时间类型字段: GETDATE()';
PRINT '- GUID类型字段: NEWID()';
PRINT '- 用户ID相关字段: ' + @FixedUserId;
PRINT '- 单位ID相关字段: ' + @FixedUnitId;

GO
