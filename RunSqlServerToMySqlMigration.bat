@echo off
echo ========================================
echo SQL Server to MySQL Migration Tool
echo ========================================
echo.
echo Database Info:
echo SQL Server: *********** - syjx database
echo MySQL: ***********:3306 - syjx database
echo.

:menu
echo Please select an option:
echo 1. Generate Basic Migration Script (SqlServerToMySqlMigration.sql)
echo 2. Generate MySQL INSERT Statements (GenerateMySqlInsertStatements.sql)
echo 3. Generate Export Script (ExportToMySqlScript.sql) - Recommended
echo 4. View All Tables Info
echo 5. Exit
echo.
echo Recommended Process:
echo   Choose 3 - Execute SELECT in SQL Server - Copy results to MySQL
echo.
set /p choice=Enter your choice (1-5):

if "%choice%"=="1" goto basic
if "%choice%"=="2" goto generate
if "%choice%"=="3" goto export
if "%choice%"=="4" goto tables
if "%choice%"=="5" goto exit
echo Invalid choice, please try again
goto menu

:basic
echo.
echo Executing basic migration script...
echo This script generates data export and MySQL import templates
sqlcmd -S *********** -U hyun -P hyun -d syjx -i SqlServerToMySqlMigration.sql
goto result

:generate
echo.
echo Generating MySQL INSERT statements...
echo This script directly generates INSERT statements for MySQL
sqlcmd -S *********** -U hyun -P hyun -d syjx -i GenerateMySqlInsertStatements.sql
goto result

:export
echo.
echo Generating export script...
echo This script generates detailed export and import instructions for each table
sqlcmd -S *********** -U hyun -P hyun -d syjx -i ExportToMySqlScript.sql
goto result

:tables
echo.
echo Viewing database table information...
echo Showing all tables with id field in syjx database
sqlcmd -S *********** -U hyun -P hyun -d syjx -Q "SELECT TABLE_NAME as TableName, (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = t.TABLE_NAME) as ColumnCount, (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = t.TABLE_NAME AND COLUMN_NAME = 'id') as HasIdField FROM INFORMATION_SCHEMA.TABLES t WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME"
goto result

:result
echo.
echo ========================================
if %ERRORLEVEL% EQU 0 (
    echo Execution completed!
) else (
    echo Execution failed! Error code: %ERRORLEVEL%
    echo Please check:
    echo 1. SQL Server connection is working
    echo 2. Database syjx exists
    echo 3. User hyun has sufficient permissions
)
echo ========================================
echo.
goto menu

:exit
echo Thank you for using!
pause
