@echo off
chcp 65001 >nul
echo ========================================
echo SQL Server到MySQL数据迁移工具
echo ========================================
echo.
echo 数据库信息：
echo SQL Server: *********** - syjx数据库
echo MySQL: ***********:3306 - syjx数据库
echo.

:menu
echo 请选择要执行的操作：
echo 1. 生成基础迁移脚本 (SqlServerToMySqlMigration.sql)
echo 2. 生成MySQL INSERT语句 (GenerateMySqlInsertStatements.sql)
echo 3. 生成导出脚本 (ExportToMySqlScript.sql) - 推荐
echo 4. 查看所有表信息
echo 5. 退出
echo.
echo 推荐流程：
echo   选择3 → 在SQL Server中执行生成的SELECT语句 → 复制结果到MySQL执行
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto basic
if "%choice%"=="2" goto generate
if "%choice%"=="3" goto export
if "%choice%"=="4" goto tables
if "%choice%"=="5" goto exit
echo 无效选择，请重新输入
goto menu

:basic
echo.
echo 正在执行基础迁移脚本...
echo 此脚本会生成数据导出和MySQL导入的模板
sqlcmd -S *********** -U hyun -P hyun -d syjx -i SqlServerToMySqlMigration.sql
goto result

:generate
echo.
echo 正在生成MySQL INSERT语句...
echo 此脚本会直接生成可在MySQL中执行的INSERT语句
sqlcmd -S *********** -U hyun -P hyun -d syjx -i GenerateMySqlInsertStatements.sql
goto result

:export
echo.
echo 正在生成导出脚本...
echo 此脚本会为每个表生成详细的导出和导入说明
sqlcmd -S *********** -U hyun -P hyun -d syjx -i ExportToMySqlScript.sql
goto result

:tables
echo.
echo 正在查看数据库表信息...
echo 显示syjx数据库中所有包含id字段的表
sqlcmd -S *********** -U hyun -P hyun -d syjx -Q "SELECT TABLE_NAME as '表名', (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = t.TABLE_NAME) as '列数', (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = t.TABLE_NAME AND COLUMN_NAME = 'id') as '有id字段' FROM INFORMATION_SCHEMA.TABLES t WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME"
goto result

:result
echo.
echo ========================================
if %ERRORLEVEL% EQU 0 (
    echo 执行完成！
) else (
    echo 执行失败！错误代码: %ERRORLEVEL%
    echo 请检查：
    echo 1. SQL Server连接是否正常
    echo 2. 数据库syjx是否存在
    echo 3. 用户hyun是否有足够权限
)
echo ========================================
echo.
goto menu

:exit
echo 感谢使用！
pause
