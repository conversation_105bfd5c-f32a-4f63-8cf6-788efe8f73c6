-- 显示表结构对比工具
-- 帮助您了解源表和目标表的实际字段，以便手动编写正确的INSERT语句

USE HyunCoreBase;
GO

PRINT '========================================';
PRINT '表结构对比工具';
PRINT '========================================';
PRINT '目的：显示源表和目标表的实际字段结构';
PRINT '用途：帮助手动编写正确的INSERT INTO SELECT语句';
PRINT '';

-- 检查 dc_PurchaseList 表
PRINT '========================================';
PRINT '表1: dc_PurchaseList';
PRINT '========================================';

IF EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'dc_PurchaseList' AND TABLE_SCHEMA = 'dbo')
BEGIN
    PRINT '源表 Hyun.dbo.dc_PurchaseList 字段:';
    SELECT 
        ROW_NUMBER() OVER (ORDER BY ORDINAL_POSITION) AS 序号,
        COLUMN_NAME AS 字段名,
        DATA_TYPE AS 数据类型,
        CASE 
            WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL 
            THEN CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10))
            WHEN NUMERIC_PRECISION IS NOT NULL 
            THEN CAST(NUMERIC_PRECISION AS VARCHAR(10)) + 
                 CASE WHEN NUMERIC_SCALE > 0 THEN ',' + CAST(NUMERIC_SCALE AS VARCHAR(10)) ELSE '' END
            ELSE ''
        END AS 长度精度,
        CASE WHEN IS_NULLABLE = 'YES' THEN '是' ELSE '否' END AS 允许空值
    FROM Hyun.INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'dc_PurchaseList' AND TABLE_SCHEMA = 'dbo'
    ORDER BY ORDINAL_POSITION;
END
ELSE
BEGIN
    PRINT '源表 Hyun.dbo.dc_PurchaseList 不存在';
END

PRINT '';

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'dc_PurchaseList' AND TABLE_SCHEMA = 'dbo')
BEGIN
    PRINT '目标表 HyunCoreBase.dbo.dc_PurchaseList 字段:';
    SELECT 
        ROW_NUMBER() OVER (ORDER BY ORDINAL_POSITION) AS 序号,
        COLUMN_NAME AS 字段名,
        DATA_TYPE AS 数据类型,
        CASE 
            WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL 
            THEN CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10))
            WHEN NUMERIC_PRECISION IS NOT NULL 
            THEN CAST(NUMERIC_PRECISION AS VARCHAR(10)) + 
                 CASE WHEN NUMERIC_SCALE > 0 THEN ',' + CAST(NUMERIC_SCALE AS VARCHAR(10)) ELSE '' END
            ELSE ''
        END AS 长度精度,
        CASE WHEN IS_NULLABLE = 'YES' THEN '是' ELSE '否' END AS 允许空值,
        CASE 
            WHEN COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 1 
            THEN '是' ELSE '否' 
        END AS 自增列
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'dc_PurchaseList' AND TABLE_SCHEMA = 'dbo'
    ORDER BY ORDINAL_POSITION;
END
ELSE
BEGIN
    PRINT '目标表 HyunCoreBase.dbo.dc_PurchaseList 不存在';
END

PRINT '';
PRINT '';

-- 检查 dc_PurchaseOrder 表
PRINT '========================================';
PRINT '表2: dc_PurchaseOrder';
PRINT '========================================';

IF EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'dc_PurchaseOrder' AND TABLE_SCHEMA = 'dbo')
BEGIN
    PRINT '源表 Hyun.dbo.dc_PurchaseOrder 字段:';
    SELECT 
        ROW_NUMBER() OVER (ORDER BY ORDINAL_POSITION) AS 序号,
        COLUMN_NAME AS 字段名,
        DATA_TYPE AS 数据类型,
        CASE 
            WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL 
            THEN CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10))
            WHEN NUMERIC_PRECISION IS NOT NULL 
            THEN CAST(NUMERIC_PRECISION AS VARCHAR(10)) + 
                 CASE WHEN NUMERIC_SCALE > 0 THEN ',' + CAST(NUMERIC_SCALE AS VARCHAR(10)) ELSE '' END
            ELSE ''
        END AS 长度精度,
        CASE WHEN IS_NULLABLE = 'YES' THEN '是' ELSE '否' END AS 允许空值
    FROM Hyun.INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'dc_PurchaseOrder' AND TABLE_SCHEMA = 'dbo'
    ORDER BY ORDINAL_POSITION;
END
ELSE
BEGIN
    PRINT '源表 Hyun.dbo.dc_PurchaseOrder 不存在';
END

PRINT '';

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'dc_PurchaseOrder' AND TABLE_SCHEMA = 'dbo')
BEGIN
    PRINT '目标表 HyunCoreBase.dbo.dc_PurchaseOrder 字段:';
    SELECT 
        ROW_NUMBER() OVER (ORDER BY ORDINAL_POSITION) AS 序号,
        COLUMN_NAME AS 字段名,
        DATA_TYPE AS 数据类型,
        CASE 
            WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL 
            THEN CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10))
            WHEN NUMERIC_PRECISION IS NOT NULL 
            THEN CAST(NUMERIC_PRECISION AS VARCHAR(10)) + 
                 CASE WHEN NUMERIC_SCALE > 0 THEN ',' + CAST(NUMERIC_SCALE AS VARCHAR(10)) ELSE '' END
            ELSE ''
        END AS 长度精度,
        CASE WHEN IS_NULLABLE = 'YES' THEN '是' ELSE '否' END AS 允许空值,
        CASE 
            WHEN COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 1 
            THEN '是' ELSE '否' 
        END AS 自增列
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'dc_PurchaseOrder' AND TABLE_SCHEMA = 'dbo'
    ORDER BY ORDINAL_POSITION;
END
ELSE
BEGIN
    PRINT '目标表 HyunCoreBase.dbo.dc_PurchaseOrder 不存在';
END

PRINT '';
PRINT '';

-- 检查 dc_PurchaseApproval 表
PRINT '========================================';
PRINT '表3: dc_PurchaseApproval';
PRINT '========================================';

IF EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'dc_PurchaseApproval' AND TABLE_SCHEMA = 'dbo')
BEGIN
    PRINT '源表 Hyun.dbo.dc_PurchaseApproval 字段:';
    SELECT 
        ROW_NUMBER() OVER (ORDER BY ORDINAL_POSITION) AS 序号,
        COLUMN_NAME AS 字段名,
        DATA_TYPE AS 数据类型,
        CASE 
            WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL 
            THEN CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10))
            WHEN NUMERIC_PRECISION IS NOT NULL 
            THEN CAST(NUMERIC_PRECISION AS VARCHAR(10)) + 
                 CASE WHEN NUMERIC_SCALE > 0 THEN ',' + CAST(NUMERIC_SCALE AS VARCHAR(10)) ELSE '' END
            ELSE ''
        END AS 长度精度,
        CASE WHEN IS_NULLABLE = 'YES' THEN '是' ELSE '否' END AS 允许空值
    FROM Hyun.INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'dc_PurchaseApproval' AND TABLE_SCHEMA = 'dbo'
    ORDER BY ORDINAL_POSITION;
END
ELSE
BEGIN
    PRINT '源表 Hyun.dbo.dc_PurchaseApproval 不存在';
END

PRINT '';

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'dc_PurchaseApproval' AND TABLE_SCHEMA = 'dbo')
BEGIN
    PRINT '目标表 HyunCoreBase.dbo.dc_PurchaseApproval 字段:';
    SELECT 
        ROW_NUMBER() OVER (ORDER BY ORDINAL_POSITION) AS 序号,
        COLUMN_NAME AS 字段名,
        DATA_TYPE AS 数据类型,
        CASE 
            WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL 
            THEN CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10))
            WHEN NUMERIC_PRECISION IS NOT NULL 
            THEN CAST(NUMERIC_PRECISION AS VARCHAR(10)) + 
                 CASE WHEN NUMERIC_SCALE > 0 THEN ',' + CAST(NUMERIC_SCALE AS VARCHAR(10)) ELSE '' END
            ELSE ''
        END AS 长度精度,
        CASE WHEN IS_NULLABLE = 'YES' THEN '是' ELSE '否' END AS 允许空值,
        CASE 
            WHEN COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 1 
            THEN '是' ELSE '否' 
        END AS 自增列
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'dc_PurchaseApproval' AND TABLE_SCHEMA = 'dbo'
    ORDER BY ORDINAL_POSITION;
END
ELSE
BEGIN
    PRINT '目标表 HyunCoreBase.dbo.dc_PurchaseApproval 不存在';
END

PRINT '';
PRINT '========================================';
PRINT '使用说明';
PRINT '========================================';
PRINT '1. 查看上面的表结构对比结果';
PRINT '2. 根据实际字段名称手动编写INSERT INTO SELECT语句';
PRINT '3. 对于目标表有但源表没有的字段，使用以下默认值：';
PRINT '   - 用户ID字段: ''592123693924485''';
PRINT '   - 单位ID字段: ''592123124052101''';
PRINT '   - 数值类型: 0';
PRINT '   - 字符串类型: ''''';
PRINT '   - 日期时间类型: GETDATE()';
PRINT '   - GUID类型: NEWID()';
PRINT '';
PRINT 'INSERT语句模板：';
PRINT 'INSERT INTO 目标表 (字段1, 字段2, ...)';
PRINT 'SELECT ';
PRINT '    源表字段1,';
PRINT '    源表字段2,';
PRINT '    ''592123693924485'' AS CreateUserId,  -- 固定用户ID';
PRINT '    ''592123124052101'' AS UnitId,        -- 固定单位ID';
PRINT '    0 AS 新增数值字段,                     -- 默认值';
PRINT '    '''' AS 新增字符串字段,                -- 默认值';
PRINT '    GETDATE() AS 新增日期字段              -- 默认值';
PRINT 'FROM Hyun.dbo.源表 src';
PRINT 'WHERE NOT EXISTS (';
PRINT '    SELECT 1 FROM 目标表 target';
PRINT '    WHERE target.Id = src.Id';
PRINT ');';
GO
