-- 最终版本：生成三个表的INSERT语句
-- 在SQL Server查询分析器中执行

-- ========================================
-- 第一步：生成 bn_Attachment 表的INSERT语句
-- ========================================

PRINT '-- =========================================='
PRINT '-- 复制以下查询并在新窗口执行，然后复制结果：'
PRINT '-- =========================================='
PRINT ''
PRINT '-- bn_Attachment 表：'
PRINT ''

-- 生成查询语句
DECLARE @query1 NVARCHAR(MAX) = 'SELECT ''INSERT INTO bn_Attachment ('

-- 获取列名
SELECT @query1 = @query1 + STUFF((
    SELECT ', ' + COLUMN_NAME
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'bn_Attachment'
    ORDER BY ORDINAL_POSITION
    FOR XML PATH('')
), 1, 2, '') + ') VALUES'''

SET @query1 = @query1 + '
UNION ALL
SELECT 
    CASE WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = 1 THEN ''('' ELSE '','' + CHAR(13) + CHAR(10) + ''('' END + '

-- 添加每列的转换逻辑
SELECT @query1 = @query1 + 
    STUFF((
        SELECT ' + '', '' + ' + 
            CASE 
                WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''' END'
                WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''' END'
                WHEN DATA_TYPE = 'date' THEN
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''' END'
                WHEN DATA_TYPE = 'bit' THEN
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
                ELSE 
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
            END
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'bn_Attachment'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 7, '')

SET @query1 = @query1 + ' + '')''
FROM bn_Attachment
UNION ALL
SELECT ''ON DUPLICATE KEY UPDATE Id = Id;'''

PRINT @query1
PRINT ''
PRINT ''

-- ========================================
-- 第二步：生成 SysUser 表的INSERT语句
-- ========================================

PRINT '-- SysUser 表：'
PRINT ''

DECLARE @query2 NVARCHAR(MAX) = 'SELECT ''INSERT INTO SysUser ('

SELECT @query2 = @query2 + STUFF((
    SELECT ', ' + COLUMN_NAME
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'SysUser'
    ORDER BY ORDINAL_POSITION
    FOR XML PATH('')
), 1, 2, '') + ') VALUES'''

SET @query2 = @query2 + '
UNION ALL
SELECT 
    CASE WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = 1 THEN ''('' ELSE '','' + CHAR(13) + CHAR(10) + ''('' END + '

SELECT @query2 = @query2 + 
    STUFF((
        SELECT ' + '', '' + ' + 
            CASE 
                WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''' END'
                WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''' END'
                WHEN DATA_TYPE = 'date' THEN
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''' END'
                WHEN DATA_TYPE = 'bit' THEN
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
                ELSE 
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
            END
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'SysUser'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 7, '')

SET @query2 = @query2 + ' + '')''
FROM SysUser
UNION ALL
SELECT ''ON DUPLICATE KEY UPDATE Id = Id;'''

PRINT @query2
PRINT ''
PRINT ''

-- ========================================
-- 第三步：生成 SysUserBelong 表的INSERT语句
-- ========================================

PRINT '-- SysUserBelong 表：'
PRINT ''

DECLARE @query3 NVARCHAR(MAX) = 'SELECT ''INSERT INTO SysUserBelong ('

SELECT @query3 = @query3 + STUFF((
    SELECT ', ' + COLUMN_NAME
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'SysUserBelong'
    ORDER BY ORDINAL_POSITION
    FOR XML PATH('')
), 1, 2, '') + ') VALUES'''

SET @query3 = @query3 + '
UNION ALL
SELECT 
    CASE WHEN ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) = 1 THEN ''('' ELSE '','' + CHAR(13) + CHAR(10) + ''('' END + '

SELECT @query3 = @query3 + 
    STUFF((
        SELECT ' + '', '' + ' + 
            CASE 
                WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''' END'
                WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''' END'
                WHEN DATA_TYPE = 'date' THEN
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''' END'
                WHEN DATA_TYPE = 'bit' THEN
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
                ELSE 
                    'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
            END
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'SysUserBelong'
        ORDER BY ORDINAL_POSITION
        FOR XML PATH('')
    ), 1, 7, '')

SET @query3 = @query3 + ' + '')''
FROM SysUserBelong
UNION ALL
SELECT ''ON DUPLICATE KEY UPDATE Id = Id;'''

PRINT @query3
PRINT ''

PRINT '-- =========================================='
PRINT '-- 使用说明：'
PRINT '-- 1. 复制上面的每个SELECT查询语句'
PRINT '-- 2. 在新的查询窗口中分别执行'
PRINT '-- 3. 复制查询结果即为最终的INSERT语句'
PRINT '-- 4. 生成的INSERT语句格式为：'
PRINT '--    INSERT INTO 表名 (列1, 列2, ...) VALUES'
PRINT '--    (值1, 值2, ...),'
PRINT '--    (值3, 值4, ...)'
PRINT '--    ON DUPLICATE KEY UPDATE Id = Id;'
PRINT '-- =========================================='
