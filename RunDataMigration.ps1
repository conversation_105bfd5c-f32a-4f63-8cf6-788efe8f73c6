# PowerShell脚本：执行数据迁移
# 连接SQL Server并执行迁移脚本

param(
    [string]$ServerName = "***********",
    [string]$Username = "hyun",
    [string]$Password = "hyun"
)

Write-Host "开始执行数据迁移..." -ForegroundColor Green

try {
    # 构建连接字符串
    $connectionString = "Server=$ServerName;User Id=$Username;Password=$Password;Integrated Security=false;"
    
    # 读取SQL脚本内容
    $sqlScript = Get-Content -Path "DataMigration.sql" -Raw
    
    # 创建SQL连接
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "已连接到SQL Server: $ServerName" -ForegroundColor Yellow
    
    # 执行SQL脚本
    $command = New-Object System.Data.SqlClient.SqlCommand($sqlScript, $connection)
    $command.CommandTimeout = 300  # 5分钟超时
    
    Write-Host "正在执行数据迁移脚本..." -ForegroundColor Yellow
    
    # 执行命令并获取结果
    $result = $command.ExecuteNonQuery()
    
    Write-Host "数据迁移执行完成！" -ForegroundColor Green
    Write-Host "影响的行数: $result" -ForegroundColor Cyan
    
} catch {
    Write-Host "执行过程中发生错误:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host $_.Exception.StackTrace -ForegroundColor Red
} finally {
    # 关闭连接
    if ($connection -and $connection.State -eq 'Open') {
        $connection.Close()
        Write-Host "数据库连接已关闭" -ForegroundColor Yellow
    }
}

Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
