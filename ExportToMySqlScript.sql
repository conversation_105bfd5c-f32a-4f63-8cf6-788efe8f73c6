-- SQL Server到MySQL数据导出脚本
-- 数据库：syjx
-- 功能：为每个表生成导出数据的SELECT语句和对应的MySQL INSERT语句

USE syjx;
GO

PRINT '-- ======================================================';
PRINT '-- SQL Server到MySQL数据迁移脚本';
PRINT '-- 数据库：syjx';
PRINT '-- 生成时间：' + CONVERT(VARCHAR(19), GETDATE(), 120);
PRINT '-- ======================================================';
PRINT '';

-- 声明变量
DECLARE @TableName NVARCHAR(128);
DECLARE @ColumnCount INT;
DECLARE @HasIdColumn BIT;

-- 获取所有表的游标
DECLARE table_cursor CURSOR FOR
SELECT TABLE_NAME
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    -- 检查表是否有id字段
    SELECT @HasIdColumn = CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = @TableName 
    AND COLUMN_NAME = 'id'
    AND DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint');

    -- 获取列数
    SELECT @ColumnCount = COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = @TableName;

    IF @HasIdColumn = 1
    BEGIN
        PRINT '-- ======================================================';
        PRINT '-- 表：' + @TableName + ' (列数：' + CAST(@ColumnCount AS VARCHAR(10)) + ')';
        PRINT '-- ======================================================';
        
        -- 1. 生成表结构信息
        PRINT '-- 表结构：';
        PRINT 'SELECT ';
        PRINT '    COLUMN_NAME,';
        PRINT '    DATA_TYPE,';
        PRINT '    IS_NULLABLE,';
        PRINT '    CHARACTER_MAXIMUM_LENGTH';
        PRINT 'FROM INFORMATION_SCHEMA.COLUMNS';
        PRINT 'WHERE TABLE_NAME = ''' + @TableName + '''';
        PRINT 'ORDER BY ORDINAL_POSITION;';
        PRINT '';

        -- 2. 生成数据导出SQL
        PRINT '-- 导出数据SQL（在SQL Server中执行）：';
        PRINT 'SELECT ';
        
        -- 动态生成列列表
        DECLARE @SelectColumns NVARCHAR(MAX) = '';
        SELECT @SelectColumns = @SelectColumns + 
            CASE 
                WHEN @SelectColumns != '' THEN ',' + CHAR(13) + '    '
                ELSE '    '
            END +
            CASE 
                WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                    'ISNULL('''''''' + REPLACE(REPLACE(' + COLUMN_NAME + ', '''''''', ''''''''''''), CHAR(13)+CHAR(10), '' '') + '''''''', ''NULL'') AS ' + COLUMN_NAME
                WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
                    'ISNULL('''''''' + CONVERT(VARCHAR(19), ' + COLUMN_NAME + ', 120) + '''''''', ''NULL'') AS ' + COLUMN_NAME
                WHEN DATA_TYPE IN ('date') THEN
                    'ISNULL('''''''' + CONVERT(VARCHAR(10), ' + COLUMN_NAME + ', 120) + '''''''', ''NULL'') AS ' + COLUMN_NAME
                WHEN DATA_TYPE IN ('time') THEN
                    'ISNULL('''''''' + CONVERT(VARCHAR(8), ' + COLUMN_NAME + ', 108) + '''''''', ''NULL'') AS ' + COLUMN_NAME
                WHEN DATA_TYPE IN ('bit') THEN
                    'CAST(ISNULL(' + COLUMN_NAME + ', 0) AS VARCHAR(1)) AS ' + COLUMN_NAME
                ELSE
                    'ISNULL(CAST(' + COLUMN_NAME + ' AS VARCHAR(MAX)), ''NULL'') AS ' + COLUMN_NAME
            END
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @TableName
        ORDER BY ORDINAL_POSITION;

        PRINT @SelectColumns;
        PRINT 'FROM ' + @TableName;
        PRINT 'ORDER BY id;';
        PRINT '';

        -- 3. 生成MySQL INSERT模板
        PRINT '-- 对应的MySQL INSERT语句模板：';
        
        -- 获取列名列表
        DECLARE @ColumnList NVARCHAR(MAX) = '';
        SELECT @ColumnList = @ColumnList + 
            CASE WHEN @ColumnList != '' THEN ', ' ELSE '' END + '`' + COLUMN_NAME + '`'
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @TableName
        ORDER BY ORDINAL_POSITION;

        PRINT 'INSERT INTO `' + @TableName + '` (' + @ColumnList + ')';
        PRINT 'VALUES';
        PRINT '-- 将上面查询的结果按以下格式粘贴：';
        PRINT '-- (value1, value2, value3, ...),';
        PRINT '-- (value1, value2, value3, ...),';
        PRINT '-- (value1, value2, value3, ...)  -- 最后一行不要逗号';
        PRINT 'ON DUPLICATE KEY UPDATE id=id;  -- 防止重复插入';
        PRINT '';

        -- 4. 生成检查重复的SQL
        PRINT '-- 检查MySQL中是否已存在数据：';
        PRINT 'SELECT COUNT(*) as existing_count FROM `' + @TableName + '`;';
        PRINT '';

        -- 5. 生成清空表的SQL（可选）
        PRINT '-- 如需清空MySQL表（谨慎使用）：';
        PRINT '-- DELETE FROM `' + @TableName + '`;';
        PRINT '-- ALTER TABLE `' + @TableName + '` AUTO_INCREMENT = 1;';
        PRINT '';
        
    END
    ELSE
    BEGIN
        PRINT '-- 跳过表 ' + @TableName + '（没有id字段或id字段类型不是整数）';
        PRINT '';
    END
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '-- ======================================================';
PRINT '-- 使用说明：';
PRINT '-- 1. 在SQL Server中执行上面的SELECT语句导出数据';
PRINT '-- 2. 将结果复制并格式化为MySQL INSERT语句';
PRINT '-- 3. 在MySQL中执行INSERT语句';
PRINT '-- 4. ON DUPLICATE KEY UPDATE确保不会重复插入相同id的记录';
PRINT '-- ======================================================';
GO
