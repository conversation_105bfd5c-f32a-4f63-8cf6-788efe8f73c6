# SQL Server 到 MySQL 数据导出工具

## 概述
本项目提供了多种方式将 SQL Server (syjx) 数据库中的所有表导出为 MySQL INSERT IGNORE 语句。每个有数据的表都会生成一个独立的 .sql 文件。

## 数据库连接信息
- **SQL Server**: 192.168.2.3, 数据库: syjx, 用户: hyun, 密码: hyun
- **MySQL**: 192.168.2.3, 数据库: syjx, 用户: root, 密码: 123456

## 可用解决方案

### 1. PowerShell 解决方案 (推荐)
**文件**: `ExportAllTables.ps1`
**运行**: 双击 `RunExportAllTables.bat`

**特点**:
- 处理所有表（不仅仅是有id字段的表）
- 生成 INSERT IGNORE 语句
- 自动跳过空表
- 处理各种数据类型转换
- 输出详细的处理信息

### 2. C# 解决方案
**文件**: `NewSqlToMySqlExporter.cs`
**运行**: 双击 `RunNewCSharpExporter.bat`

**特点**:
- .NET 6.0 控制台应用程序
- 完整的数据类型处理
- 错误处理和详细日志
- 自动创建输出目录

### 3. 旧版 PowerShell 解决方案
**文件**: `SimplePowerShellExporter.ps1`
**运行**: 双击 `RunSimplePowerShell.bat`

## 输出结果
所有解决方案都会在 `mysql_scripts` 目录下生成以下格式的文件：
- `表名_mysql_insert.sql`

每个文件包含：
```sql
-- MySQL Insert Script - Table: 表名
-- Generated: 2024-01-01 12:00:00
-- Row Count: 100
-- Note: Using INSERT IGNORE to skip duplicate records

INSERT IGNORE INTO `表名` (`字段1`, `字段2`, `字段3`) VALUES
('值1', '值2', '值3'),
('值4', '值5', '值6'),
...
('最后值1', '最后值2', '最后值3');
```

## 数据类型处理
- **字符串类型**: varchar, nvarchar, char, nchar, text, ntext - 自动转义单引号和特殊字符
- **日期时间**: datetime, datetime2, smalldatetime - 格式化为 'YYYY-MM-DD HH:mm:ss'
- **日期**: date - 格式化为 'YYYY-MM-DD'
- **布尔**: bit - 转换为 1 或 0
- **数值**: int, bigint, decimal 等 - 直接输出
- **NULL值**: 输出为 NULL

## INSERT IGNORE 说明
使用 INSERT IGNORE 语句的好处：
- 如果记录已存在（基于主键或唯一索引），会静默跳过
- 不会因为重复记录而报错
- 适合数据迁移场景

## 使用步骤
1. 确保 SQL Server 数据库可访问
2. 选择一个解决方案（推荐 PowerShell）
3. 双击对应的 .bat 文件运行
4. 等待处理完成
5. 检查 `mysql_scripts` 目录下的生成文件
6. 在 MySQL 中执行这些 .sql 文件

## 注意事项
- 确保有足够的磁盘空间存储生成的 SQL 文件
- 大表可能需要较长时间处理
- 建议先在测试环境中验证生成的 SQL 语句
- 如果遇到特殊字符问题，检查数据库字符集设置

## 故障排除
1. **连接失败**: 检查数据库服务器是否可访问，用户名密码是否正确
2. **权限错误**: 确保用户有读取所有表的权限
3. **PowerShell 执行策略**: 如果 PowerShell 脚本无法运行，以管理员身份运行 PowerShell 并执行：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```
4. **C# 编译错误**: 确保安装了 .NET 6.0 SDK

## 技术支持
如有问题，请检查：
- 数据库连接字符串是否正确
- 网络连接是否正常
- 用户权限是否足够
- 输出目录是否有写入权限
