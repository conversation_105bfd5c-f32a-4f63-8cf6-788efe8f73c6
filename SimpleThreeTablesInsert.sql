-- 简单版本：直接生成三个表的INSERT语句
-- 每条数据生成一个INSERT语句，包含ON DUPLICATE KEY UPDATE

-- ========================================
-- 生成 bn_Attachment 表的INSERT语句
-- ========================================

PRINT '-- =========================================='
PRINT '-- bn_Attachment 表的INSERT语句'
PRINT '-- =========================================='

-- 检查表是否存在
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'bn_Attachment')
BEGIN
    -- 动态生成INSERT语句
    DECLARE @sql NVARCHAR(MAX) = '
    SELECT 
        ''INSERT INTO bn_Attachment ('' + 
        ''' + 
        STUFF((
            SELECT ', ' + COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'bn_Attachment'
            ORDER BY ORDINAL_POSITION
            FOR XML PATH('')
        ), 1, 2, '') + 
        ''') VALUES ('' + ' +
        STUFF((
            SELECT ' + '', '' + ' + 
                CASE 
                    WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''''' END'
                    WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                    WHEN DATA_TYPE = 'date' THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                    WHEN DATA_TYPE = 'bit' THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
                    ELSE 
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
                END
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'bn_Attachment'
            ORDER BY ORDINAL_POSITION
            FOR XML PATH('')
        ), 1, 7, '') + ' +
        '') ON DUPLICATE KEY UPDATE Id = Id;''
    FROM bn_Attachment'
    
    EXEC sp_executesql @sql
END
ELSE
BEGIN
    PRINT '-- 表 bn_Attachment 不存在'
END

PRINT ''
PRINT ''

-- ========================================
-- 生成 SysUser 表的INSERT语句
-- ========================================

PRINT '-- =========================================='
PRINT '-- SysUser 表的INSERT语句'
PRINT '-- =========================================='

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SysUser')
BEGIN
    SET @sql = '
    SELECT 
        ''INSERT INTO SysUser ('' + 
        ''' + 
        STUFF((
            SELECT ', ' + COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'SysUser'
            ORDER BY ORDINAL_POSITION
            FOR XML PATH('')
        ), 1, 2, '') + 
        ''') VALUES ('' + ' +
        STUFF((
            SELECT ' + '', '' + ' + 
                CASE 
                    WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''''' END'
                    WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                    WHEN DATA_TYPE = 'date' THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                    WHEN DATA_TYPE = 'bit' THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
                    ELSE 
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
                END
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'SysUser'
            ORDER BY ORDINAL_POSITION
            FOR XML PATH('')
        ), 1, 7, '') + ' +
        '') ON DUPLICATE KEY UPDATE Id = Id;''
    FROM SysUser'
    
    EXEC sp_executesql @sql
END
ELSE
BEGIN
    PRINT '-- 表 SysUser 不存在'
END

PRINT ''
PRINT ''

-- ========================================
-- 生成 SysUserBelong 表的INSERT语句
-- ========================================

PRINT '-- =========================================='
PRINT '-- SysUserBelong 表的INSERT语句'
PRINT '-- =========================================='

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'SysUserBelong')
BEGIN
    SET @sql = '
    SELECT 
        ''INSERT INTO SysUserBelong ('' + 
        ''' + 
        STUFF((
            SELECT ', ' + COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'SysUserBelong'
            ORDER BY ORDINAL_POSITION
            FOR XML PATH('')
        ), 1, 2, '') + 
        ''') VALUES ('' + ' +
        STUFF((
            SELECT ' + '', '' + ' + 
                CASE 
                    WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + REPLACE(CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)), '''''''', '''''''''''') + '''''''''' END'
                    WHEN DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime') THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(19), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                    WHEN DATA_TYPE = 'date' THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE '''''''''' + CONVERT(VARCHAR(10), [' + COLUMN_NAME + '], 120) + '''''''''' END'
                    WHEN DATA_TYPE = 'bit' THEN
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS VARCHAR(1)) END'
                    ELSE 
                        'CASE WHEN [' + COLUMN_NAME + '] IS NULL THEN ''NULL'' ELSE CAST([' + COLUMN_NAME + '] AS NVARCHAR(MAX)) END'
                END
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'SysUserBelong'
            ORDER BY ORDINAL_POSITION
            FOR XML PATH('')
        ), 1, 7, '') + ' +
        '') ON DUPLICATE KEY UPDATE Id = Id;''
    FROM SysUserBelong'
    
    EXEC sp_executesql @sql
END
ELSE
BEGIN
    PRINT '-- 表 SysUserBelong 不存在'
END

PRINT ''
PRINT '-- =========================================='
PRINT '-- 脚本执行完成'
PRINT '-- =========================================='
